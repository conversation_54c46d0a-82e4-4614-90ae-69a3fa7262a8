import React from 'react';
import { LogOut, User, <PERSON><PERSON><PERSON>, <PERSON>, Menu } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

interface NavbarProps {
  onMenuClick: () => void;
}

const Navbar = ({ onMenuClick }: NavbarProps) => {
  const { user, logout } = useAuth();

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'landlord': return 'Landlord';
      case 'property_manager': return 'Property Manager';
      case 'caretaker': return 'Caretaker';
      case 'tenant': return 'Tenant';
      default: return 'User';
    }
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Mobile menu button */}
          <button
            className="lg:hidden p-2 text-gray-600 hover:text-gray-900 transition-colors"
            onClick={onMenuClick}
          >
            <Menu size={20} />
          </button>

          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">PropertyPro</h1>
          <span className="hidden sm:inline text-sm text-gray-500">Enterprise Property Management</span>
        </div>

        <div className="flex items-center space-x-2 sm:space-x-4">
          <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
            <Bell size={18} className="sm:w-5 sm:h-5" />
          </button>

          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="hidden sm:block text-right">
              <div className="text-sm font-medium text-gray-900">{user?.name}</div>
              <div className="text-xs text-gray-500">{getRoleDisplayName(user?.role || '')}</div>
            </div>

            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <User size={16} className="text-white" />
            </div>

            <div className="flex items-center space-x-1 sm:space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Settings size={16} />
              </button>
              <button
                onClick={logout}
                className="p-2 text-gray-400 hover:text-red-600 transition-colors"
              >
                <LogOut size={16} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;