import { executeQuery, executeTransaction } from '../config/database.js';
import { Lease, PaginationQuery } from '../types/index.js';
import { v4 as uuidv4 } from 'uuid';

export class LeaseModel {
  // Create a new lease
  static async create(leaseData: Partial<Lease>): Promise<Lease> {
    const id = uuidv4();
    
    const queries = [
      {
        query: `
          INSERT INTO leases (
            id, unit_id, tenant_id, lease_type, start_date, end_date,
            monthly_rent, security_deposit, late_fee_amount, late_fee_type,
            grace_period_days, lease_document_url, status, auto_renew,
            renewal_notice_days, special_terms
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        params: [
          id,
          leaseData.unit_id,
          leaseData.tenant_id,
          leaseData.lease_type || 'fixed',
          leaseData.start_date,
          leaseData.end_date,
          leaseData.monthly_rent,
          leaseData.security_deposit,
          leaseData.late_fee_amount || 0,
          leaseData.late_fee_type || 'flat',
          leaseData.grace_period_days || 5,
          leaseData.lease_document_url || null,
          leaseData.status || 'draft',
          leaseData.auto_renew || false,
          leaseData.renewal_notice_days || 30,
          leaseData.special_terms || null
        ]
      },
      {
        query: `UPDATE units SET is_occupied = TRUE, is_available = FALSE WHERE id = ?`,
        params: [leaseData.unit_id]
      }
    ];
    
    await executeTransaction(queries);
    
    return await this.findById(id) as Lease;
  }

  // Find lease by ID
  static async findById(id: string): Promise<Lease | null> {
    const query = `
      SELECT l.*, 
             CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
             u.email as tenant_email, u.phone as tenant_phone,
             un.unit_number, un.unit_type, un.bedrooms, un.bathrooms,
             p.name as property_name, p.address as property_address,
             p.city as property_city,
             CONCAT(landlord.first_name, ' ', landlord.last_name) as landlord_name,
             CONCAT(manager.first_name, ' ', manager.last_name) as manager_name
      FROM leases l
      JOIN tenants t ON l.tenant_id = t.id
      JOIN users u ON t.user_id = u.id
      JOIN units un ON l.unit_id = un.id
      JOIN properties p ON un.property_id = p.id
      LEFT JOIN users landlord ON p.landlord_id = landlord.id
      LEFT JOIN users manager ON p.manager_id = manager.id
      WHERE l.id = ?
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  // Update lease
  static async update(id: string, updateData: Partial<Lease>): Promise<Lease | null> {
    const allowedFields = [
      'lease_type', 'start_date', 'end_date', 'monthly_rent', 'security_deposit',
      'late_fee_amount', 'late_fee_type', 'grace_period_days', 'lease_document_url',
      'status', 'termination_date', 'termination_reason', 'auto_renew',
      'renewal_notice_days', 'special_terms'
    ];
    
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key) && updateData[key as keyof Lease] !== undefined) {
        updates.push(`${key} = ?`);
        params.push(updateData[key as keyof Lease]);
      }
    });
    
    if (updates.length === 0) {
      return await this.findById(id);
    }
    
    params.push(id);
    
    const query = `
      UPDATE leases 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    await executeQuery(query, params);
    
    // Update unit occupancy status based on lease status
    if (updateData.status) {
      const lease = await this.findById(id);
      if (lease) {
        const isOccupied = ['active'].includes(updateData.status);
        const isAvailable = ['terminated', 'expired'].includes(updateData.status);
        
        await executeQuery(
          `UPDATE units SET is_occupied = ?, is_available = ? WHERE id = ?`,
          [isOccupied, isAvailable, lease.unit_id]
        );
      }
    }
    
    return await this.findById(id);
  }

  // Get all leases with pagination and filtering
  static async findAll(
    options: PaginationQuery & {
      landlordId?: string;
      propertyId?: string;
      tenantId?: string;
      status?: string;
      expiringWithin?: number; // days
    } = {}
  ): Promise<{ leases: Lease[]; total: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      landlordId,
      propertyId,
      tenantId,
      status,
      expiringWithin
    } = options;
    
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ` AND p.landlord_id = ?`;
      params.push(landlordId);
    }
    
    if (propertyId) {
      whereClause += ` AND un.property_id = ?`;
      params.push(propertyId);
    }
    
    if (tenantId) {
      whereClause += ` AND l.tenant_id = ?`;
      params.push(tenantId);
    }
    
    if (status) {
      whereClause += ` AND l.status = ?`;
      params.push(status);
    }
    
    if (expiringWithin) {
      whereClause += ` AND l.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY)`;
      params.push(expiringWithin);
    }
    
    if (search) {
      whereClause += ` AND (CONCAT(u.first_name, ' ', u.last_name) LIKE ? OR un.unit_number LIKE ? OR p.name LIKE ?)`;
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }
    
    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM leases l
      JOIN tenants t ON l.tenant_id = t.id
      JOIN users u ON t.user_id = u.id
      JOIN units un ON l.unit_id = un.id
      JOIN properties p ON un.property_id = p.id
      ${whereClause}
    `;
    
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;
    
    // Get leases
    const query = `
      SELECT l.*, 
             CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
             u.email as tenant_email,
             un.unit_number, un.unit_type,
             p.name as property_name, p.address as property_address
      FROM leases l
      JOIN tenants t ON l.tenant_id = t.id
      JOIN users u ON t.user_id = u.id
      JOIN units un ON l.unit_id = un.id
      JOIN properties p ON un.property_id = p.id
      ${whereClause}
      ORDER BY l.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const leases = await executeQuery(query, params);
    
    return { leases, total };
  }

  // Terminate lease
  static async terminate(id: string, terminationReason: string): Promise<Lease | null> {
    const queries = [
      {
        query: `
          UPDATE leases 
          SET status = 'terminated', termination_date = CURRENT_TIMESTAMP, 
              termination_reason = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `,
        params: [terminationReason, id]
      }
    ];
    
    // Get lease to update unit status
    const lease = await this.findById(id);
    if (lease) {
      queries.push({
        query: `UPDATE units SET is_occupied = FALSE, is_available = TRUE WHERE id = ?`,
        params: [lease.unit_id]
      });
    }
    
    await executeTransaction(queries);
    
    return await this.findById(id);
  }

  // Renew lease
  static async renew(id: string, renewalData: {
    new_end_date: Date;
    new_monthly_rent?: number;
    special_terms?: string;
  }): Promise<Lease | null> {
    const updateData: Partial<Lease> = {
      end_date: renewalData.new_end_date,
      status: 'active'
    };
    
    if (renewalData.new_monthly_rent) {
      updateData.monthly_rent = renewalData.new_monthly_rent;
    }
    
    if (renewalData.special_terms) {
      updateData.special_terms = renewalData.special_terms;
    }
    
    return await this.update(id, updateData);
  }

  // Get expiring leases
  static async getExpiringLeases(days: number = 30, landlordId?: string): Promise<Lease[]> {
    let whereClause = `WHERE l.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY) 
                       AND l.status = 'active'`;
    const params: any[] = [days];
    
    if (landlordId) {
      whereClause += ` AND p.landlord_id = ?`;
      params.push(landlordId);
    }
    
    const query = `
      SELECT l.*, 
             CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
             u.email as tenant_email, u.phone as tenant_phone,
             un.unit_number, p.name as property_name
      FROM leases l
      JOIN tenants t ON l.tenant_id = t.id
      JOIN users u ON t.user_id = u.id
      JOIN units un ON l.unit_id = un.id
      JOIN properties p ON un.property_id = p.id
      ${whereClause}
      ORDER BY l.end_date ASC
    `;
    
    return await executeQuery(query, params);
  }

  // Get lease statistics
  static async getStats(landlordId?: string): Promise<any> {
    let whereClause = '';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause = 'WHERE p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        COUNT(*) as total_leases,
        COUNT(CASE WHEN l.status = 'active' THEN 1 END) as active_leases,
        COUNT(CASE WHEN l.status = 'expired' THEN 1 END) as expired_leases,
        COUNT(CASE WHEN l.status = 'terminated' THEN 1 END) as terminated_leases,
        COUNT(CASE WHEN l.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY) THEN 1 END) as expiring_soon,
        AVG(l.monthly_rent) as average_rent,
        SUM(CASE WHEN l.status = 'active' THEN l.monthly_rent ELSE 0 END) as total_monthly_revenue
      FROM leases l
      JOIN units un ON l.unit_id = un.id
      JOIN properties p ON un.property_id = p.id
      ${whereClause}
    `;
    
    const results = await executeQuery(query, params);
    return results[0];
  }

  // Check for lease conflicts (overlapping leases for same unit)
  static async checkConflicts(unitId: string, startDate: Date, endDate: Date, excludeLeaseId?: string): Promise<boolean> {
    let query = `
      SELECT COUNT(*) as count 
      FROM leases 
      WHERE unit_id = ? 
        AND status IN ('active', 'draft')
        AND (
          (start_date <= ? AND end_date >= ?) OR
          (start_date <= ? AND end_date >= ?) OR
          (start_date >= ? AND end_date <= ?)
        )
    `;
    
    const params = [unitId, startDate, startDate, endDate, endDate, startDate, endDate];
    
    if (excludeLeaseId) {
      query += ` AND id != ?`;
      params.push(excludeLeaseId);
    }
    
    const result = await executeQuery(query, params);
    return result[0].count > 0;
  }
}
