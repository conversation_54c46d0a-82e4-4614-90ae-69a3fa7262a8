import { Property, Unit, Tenant, Lease, Invoice, MaintenanceRequest, Payment } from '../types';

export const mockProperties: Property[] = [
  {
    id: '1',
    name: 'Sunset Apartments',
    address: '123 Sunset Blvd, Los Angeles, CA',
    type: 'residential',
    units: [],
    landlordId: '1',
    managerId: '2',
    createdAt: new Date('2023-01-15')
  },
  {
    id: '2',
    name: 'Downtown Office Complex',
    address: '456 Business St, Los Angeles, CA',
    type: 'commercial',
    units: [],
    landlordId: '1',
    createdAt: new Date('2023-03-20')
  }
];

export const mockTenants: Tenant[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    emergencyContact: '******-0124'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0125',
    emergencyContact: '******-0126'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0127',
    emergencyContact: '******-0128'
  }
];

export const mockUnits: Unit[] = [
  {
    id: '1',
    propertyId: '1',
    unitNumber: 'A101',
    type: '2br',
    rent: 2500,
    deposit: 5000,
    isOccupied: true,
    tenant: mockTenants[0]
  },
  {
    id: '2',
    propertyId: '1',
    unitNumber: 'A102',
    type: '1br',
    rent: 1800,
    deposit: 3600,
    isOccupied: true,
    tenant: mockTenants[1]
  },
  {
    id: '3',
    propertyId: '1',
    unitNumber: 'A103',
    type: '2br',
    rent: 2500,
    deposit: 5000,
    isOccupied: false
  },
  {
    id: '4',
    propertyId: '2',
    unitNumber: 'Suite 201',
    type: 'office',
    rent: 3500,
    deposit: 7000,
    isOccupied: true,
    tenant: mockTenants[2]
  }
];

export const mockLeases: Lease[] = [
  {
    id: '1',
    unitId: '1',
    tenantId: '1',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    monthlyRent: 2500,
    deposit: 5000,
    status: 'active'
  },
  {
    id: '2',
    unitId: '2',
    tenantId: '2',
    startDate: new Date('2024-02-01'),
    endDate: new Date('2025-01-31'),
    monthlyRent: 1800,
    deposit: 3600,
    status: 'active'
  },
  {
    id: '3',
    unitId: '4',
    tenantId: '3',
    startDate: new Date('2024-03-01'),
    endDate: new Date('2025-02-28'),
    monthlyRent: 3500,
    deposit: 7000,
    status: 'active'
  }
];

export const mockInvoices: Invoice[] = [
  {
    id: '1',
    leaseId: '1',
    tenantId: '1',
    amount: 2500,
    dueDate: new Date('2024-12-01'),
    status: 'paid',
    paidAmount: 2500,
    lateFee: 0,
    issueDate: new Date('2024-11-01'),
    paymentDate: new Date('2024-11-28')
  },
  {
    id: '2',
    leaseId: '2',
    tenantId: '2',
    amount: 1800,
    dueDate: new Date('2024-12-01'),
    status: 'pending',
    paidAmount: 0,
    lateFee: 0,
    issueDate: new Date('2024-11-01')
  },
  {
    id: '3',
    leaseId: '3',
    tenantId: '3',
    amount: 3500,
    dueDate: new Date('2024-11-15'),
    status: 'overdue',
    paidAmount: 0,
    lateFee: 175,
    issueDate: new Date('2024-10-15')
  }
];

export const mockMaintenanceRequests: MaintenanceRequest[] = [
  {
    id: '1',
    unitId: '1',
    tenantId: '1',
    title: 'Leaking Faucet',
    description: 'Kitchen faucet is dripping continuously',
    priority: 'medium',
    status: 'in_progress',
    category: 'plumbing',
    createdAt: new Date('2024-11-20'),
    updatedAt: new Date('2024-11-21'),
    assignedTo: '3',
    estimatedCost: 150
  },
  {
    id: '2',
    unitId: '2',
    tenantId: '2',
    title: 'AC Not Working',
    description: 'Air conditioning unit stopped working',
    priority: 'high',
    status: 'pending',
    category: 'hvac',
    createdAt: new Date('2024-11-22'),
    updatedAt: new Date('2024-11-22'),
    estimatedCost: 500
  },
  {
    id: '3',
    unitId: '4',
    tenantId: '3',
    title: 'Flickering Lights',
    description: 'Office lights are flickering intermittently',
    priority: 'low',
    status: 'completed',
    category: 'electrical',
    createdAt: new Date('2024-11-15'),
    updatedAt: new Date('2024-11-18'),
    assignedTo: '3',
    estimatedCost: 200,
    actualCost: 180
  }
];

export const mockPayments: Payment[] = [
  {
    id: '1',
    invoiceId: '1',
    amount: 2500,
    method: 'mpesa',
    transactionRef: 'MP2411280001',
    date: new Date('2024-11-28'),
    status: 'completed'
  }
];