import { executeQuery } from '../config/database.js';

export class ReportModel {
  // Financial Performance Report
  static async getFinancialReport(
    landlordId?: string,
    dateFrom?: Date,
    dateTo?: Date,
    propertyId?: string
  ): Promise<any> {
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ' AND p.landlord_id = ?';
      params.push(landlordId);
    }
    
    if (propertyId) {
      whereClause += ' AND p.id = ?';
      params.push(propertyId);
    }
    
    if (dateFrom) {
      whereClause += ' AND (pay.payment_date >= ? OR i.issue_date >= ?)';
      params.push(dateFrom, dateFrom);
    }
    
    if (dateTo) {
      whereClause += ' AND (pay.payment_date <= ? OR i.issue_date <= ?)';
      params.push(dateTo, dateTo);
    }
    
    const query = `
      SELECT 
        -- Revenue Metrics
        COALESCE(SUM(CASE WHEN pay.status = 'completed' THEN pay.amount END), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount END), 0) as total_invoiced_paid,
        COALESCE(SUM(CASE WHEN i.status IN ('sent', 'viewed') THEN i.total_amount END), 0) as outstanding_amount,
        COALESCE(SUM(CASE WHEN i.due_date < NOW() AND i.status IN ('sent', 'viewed') THEN i.total_amount END), 0) as overdue_amount,
        
        -- Property Metrics
        COUNT(DISTINCT p.id) as total_properties,
        COUNT(DISTINCT u.id) as total_units,
        COUNT(DISTINCT CASE WHEN u.is_occupied = TRUE THEN u.id END) as occupied_units,
        
        -- Tenant Metrics
        COUNT(DISTINCT t.id) as total_tenants,
        COUNT(DISTINCT CASE WHEN l.status = 'active' THEN l.id END) as active_leases,
        
        -- Payment Method Breakdown
        COALESCE(SUM(CASE WHEN pay.payment_method = 'mpesa' AND pay.status = 'completed' THEN pay.amount END), 0) as mpesa_revenue,
        COALESCE(SUM(CASE WHEN pay.payment_method = 'bank_transfer' AND pay.status = 'completed' THEN pay.amount END), 0) as bank_revenue,
        COALESCE(SUM(CASE WHEN pay.payment_method = 'card' AND pay.status = 'completed' THEN pay.amount END), 0) as card_revenue,
        
        -- Monthly Potential Revenue
        COALESCE(SUM(l.monthly_rent), 0) as potential_monthly_revenue,
        COALESCE(SUM(CASE WHEN l.status = 'active' THEN l.monthly_rent END), 0) as actual_monthly_revenue
        
      FROM properties p
      LEFT JOIN units u ON p.id = u.property_id
      LEFT JOIN leases l ON u.id = l.unit_id
      LEFT JOIN tenants t ON l.tenant_id = t.id
      LEFT JOIN invoices i ON l.id = i.lease_id
      LEFT JOIN payments pay ON i.id = pay.invoice_id
      ${whereClause}
    `;
    
    const results = await executeQuery(query, params);
    const report = results[0];
    
    // Calculate additional metrics
    report.occupancy_rate = report.total_units > 0 ? 
      Math.round((report.occupied_units / report.total_units) * 100) : 0;
    
    report.collection_rate = report.total_invoiced_paid > 0 ? 
      Math.round((report.total_revenue / report.total_invoiced_paid) * 100) : 0;
    
    report.revenue_efficiency = report.potential_monthly_revenue > 0 ? 
      Math.round((report.actual_monthly_revenue / report.potential_monthly_revenue) * 100) : 0;
    
    return report;
  }

  // Property Performance Report
  static async getPropertyPerformanceReport(landlordId?: string): Promise<any[]> {
    let whereClause = '';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause = 'WHERE p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        p.id,
        p.name,
        p.address,
        p.city,
        p.property_type,
        COUNT(DISTINCT u.id) as total_units,
        COUNT(DISTINCT CASE WHEN u.is_occupied = TRUE THEN u.id END) as occupied_units,
        COUNT(DISTINCT CASE WHEN l.status = 'active' THEN l.id END) as active_leases,
        COALESCE(SUM(l.monthly_rent), 0) as potential_monthly_revenue,
        COALESCE(SUM(CASE WHEN l.status = 'active' THEN l.monthly_rent END), 0) as actual_monthly_revenue,
        COALESCE(SUM(CASE WHEN pay.status = 'completed' AND pay.payment_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH) THEN pay.amount END), 0) as revenue_12_months,
        COALESCE(AVG(CASE WHEN l.status = 'active' THEN l.monthly_rent END), 0) as average_rent,
        COUNT(DISTINCT CASE WHEN mr.status = 'open' THEN mr.id END) as open_maintenance_requests,
        COUNT(DISTINCT CASE WHEN i.status IN ('sent', 'viewed') THEN i.id END) as outstanding_invoices,
        COALESCE(SUM(CASE WHEN i.status IN ('sent', 'viewed') THEN i.total_amount END), 0) as outstanding_amount
      FROM properties p
      LEFT JOIN units u ON p.id = u.property_id
      LEFT JOIN leases l ON u.id = l.unit_id
      LEFT JOIN invoices i ON l.id = i.lease_id
      LEFT JOIN payments pay ON i.id = pay.invoice_id
      LEFT JOIN maintenance_requests mr ON u.id = mr.unit_id
      ${whereClause}
      GROUP BY p.id, p.name, p.address, p.city, p.property_type
      ORDER BY actual_monthly_revenue DESC
    `;
    
    const properties = await executeQuery(query, params);
    
    // Calculate additional metrics for each property
    return properties.map((property: any) => ({
      ...property,
      occupancy_rate: property.total_units > 0 ? 
        Math.round((property.occupied_units / property.total_units) * 100) : 0,
      revenue_efficiency: property.potential_monthly_revenue > 0 ? 
        Math.round((property.actual_monthly_revenue / property.potential_monthly_revenue) * 100) : 0,
      annual_revenue_projection: property.actual_monthly_revenue * 12
    }));
  }

  // Tenant Performance Report
  static async getTenantPerformanceReport(landlordId?: string): Promise<any[]> {
    let whereClause = '';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause = 'WHERE p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        t.id,
        CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
        u.email,
        u.phone,
        t.move_in_date,
        l.start_date as lease_start,
        l.end_date as lease_end,
        l.monthly_rent,
        l.status as lease_status,
        un.unit_number,
        p.name as property_name,
        COUNT(DISTINCT i.id) as total_invoices,
        COUNT(DISTINCT CASE WHEN i.status = 'paid' THEN i.id END) as paid_invoices,
        COUNT(DISTINCT CASE WHEN i.due_date < NOW() AND i.status IN ('sent', 'viewed') THEN i.id END) as overdue_invoices,
        COALESCE(SUM(CASE WHEN pay.status = 'completed' THEN pay.amount END), 0) as total_payments,
        COALESCE(SUM(CASE WHEN i.status IN ('sent', 'viewed') THEN i.total_amount END), 0) as outstanding_amount,
        COUNT(DISTINCT mr.id) as maintenance_requests,
        AVG(DATEDIFF(pay.payment_date, i.due_date)) as average_payment_delay_days
      FROM tenants t
      JOIN users u ON t.user_id = u.id
      LEFT JOIN leases l ON t.id = l.tenant_id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      LEFT JOIN invoices i ON l.id = i.lease_id
      LEFT JOIN payments pay ON i.id = pay.invoice_id
      LEFT JOIN maintenance_requests mr ON t.id = mr.tenant_id
      ${whereClause}
      GROUP BY t.id, u.first_name, u.last_name, u.email, u.phone, t.move_in_date, 
               l.start_date, l.end_date, l.monthly_rent, l.status, un.unit_number, p.name
      ORDER BY total_payments DESC
    `;
    
    const tenants = await executeQuery(query, params);
    
    // Calculate additional metrics for each tenant
    return tenants.map((tenant: any) => ({
      ...tenant,
      payment_rate: tenant.total_invoices > 0 ? 
        Math.round((tenant.paid_invoices / tenant.total_invoices) * 100) : 0,
      is_good_payer: tenant.overdue_invoices === 0 && tenant.outstanding_amount === 0,
      risk_level: tenant.overdue_invoices > 2 ? 'high' : 
                  tenant.overdue_invoices > 0 ? 'medium' : 'low'
    }));
  }

  // Occupancy Report
  static async getOccupancyReport(landlordId?: string, months: number = 12): Promise<any> {
    let whereClause = '';
    const params: any[] = [months];
    
    if (landlordId) {
      whereClause = 'AND p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        DATE_FORMAT(date_series.month, '%Y-%m') as month,
        COUNT(DISTINCT p.id) as total_properties,
        COUNT(DISTINCT u.id) as total_units,
        COUNT(DISTINCT CASE 
          WHEN l.start_date <= LAST_DAY(date_series.month) 
           AND (l.end_date >= date_series.month OR l.status = 'active')
          THEN u.id 
        END) as occupied_units,
        COALESCE(SUM(CASE 
          WHEN l.start_date <= LAST_DAY(date_series.month) 
           AND (l.end_date >= date_series.month OR l.status = 'active')
          THEN l.monthly_rent 
        END), 0) as monthly_revenue
      FROM (
        SELECT DATE_SUB(CURDATE(), INTERVAL n MONTH) as month
        FROM (
          SELECT 0 as n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION 
          SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
          SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11
        ) numbers
        WHERE n < ?
      ) date_series
      CROSS JOIN properties p
      LEFT JOIN units u ON p.id = u.property_id
      LEFT JOIN leases l ON u.id = l.unit_id
      WHERE 1=1 ${whereClause}
      GROUP BY date_series.month
      ORDER BY date_series.month DESC
    `;
    
    const monthlyData = await executeQuery(query, params);
    
    // Calculate occupancy rates
    const occupancyTrend = monthlyData.map((month: any) => ({
      ...month,
      occupancy_rate: month.total_units > 0 ? 
        Math.round((month.occupied_units / month.total_units) * 100) : 0
    }));
    
    // Calculate summary statistics
    const totalUnits = monthlyData[0]?.total_units || 0;
    const currentOccupied = monthlyData[0]?.occupied_units || 0;
    const averageOccupancy = monthlyData.length > 0 ? 
      Math.round(monthlyData.reduce((sum: number, month: any) => 
        sum + (month.total_units > 0 ? (month.occupied_units / month.total_units) * 100 : 0), 0
      ) / monthlyData.length) : 0;
    
    return {
      current_occupancy_rate: totalUnits > 0 ? Math.round((currentOccupied / totalUnits) * 100) : 0,
      average_occupancy_rate: averageOccupancy,
      total_units: totalUnits,
      occupied_units: currentOccupied,
      vacant_units: totalUnits - currentOccupied,
      monthly_trend: occupancyTrend
    };
  }

  // Revenue Trend Report
  static async getRevenueTrendReport(landlordId?: string, months: number = 12): Promise<any> {
    let whereClause = '';
    const params: any[] = [months];
    
    if (landlordId) {
      whereClause = 'AND p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        DATE_FORMAT(pay.payment_date, '%Y-%m') as month,
        COUNT(DISTINCT pay.id) as payment_count,
        COALESCE(SUM(CASE WHEN pay.status = 'completed' THEN pay.amount END), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN pay.payment_method = 'mpesa' AND pay.status = 'completed' THEN pay.amount END), 0) as mpesa_revenue,
        COALESCE(SUM(CASE WHEN pay.payment_method = 'bank_transfer' AND pay.status = 'completed' THEN pay.amount END), 0) as bank_revenue,
        COALESCE(SUM(CASE WHEN pay.payment_method = 'card' AND pay.status = 'completed' THEN pay.amount END), 0) as card_revenue,
        COUNT(DISTINCT t.id) as paying_tenants
      FROM payments pay
      JOIN invoices i ON pay.invoice_id = i.id
      JOIN leases l ON i.lease_id = l.id
      JOIN units u ON l.unit_id = u.id
      JOIN properties p ON u.property_id = p.id
      JOIN tenants t ON pay.tenant_id = t.id
      WHERE pay.payment_date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
        ${whereClause}
      GROUP BY DATE_FORMAT(pay.payment_date, '%Y-%m')
      ORDER BY month DESC
    `;
    
    const monthlyRevenue = await executeQuery(query, params);
    
    // Calculate growth rates
    const revenueWithGrowth = monthlyRevenue.map((month: any, index: number) => {
      const previousMonth = monthlyRevenue[index + 1];
      const growthRate = previousMonth && previousMonth.total_revenue > 0 ? 
        Math.round(((month.total_revenue - previousMonth.total_revenue) / previousMonth.total_revenue) * 100) : 0;
      
      return {
        ...month,
        growth_rate: growthRate
      };
    });
    
    // Calculate summary statistics
    const totalRevenue = monthlyRevenue.reduce((sum: number, month: any) => sum + month.total_revenue, 0);
    const averageMonthlyRevenue = monthlyRevenue.length > 0 ? totalRevenue / monthlyRevenue.length : 0;
    
    return {
      total_revenue_period: totalRevenue,
      average_monthly_revenue: Math.round(averageMonthlyRevenue),
      monthly_data: revenueWithGrowth
    };
  }

  // Maintenance Report
  static async getMaintenanceReport(landlordId?: string): Promise<any> {
    let whereClause = '';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause = 'WHERE p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        COUNT(*) as total_requests,
        COUNT(CASE WHEN mr.status = 'open' THEN 1 END) as open_requests,
        COUNT(CASE WHEN mr.status = 'in_progress' THEN 1 END) as in_progress_requests,
        COUNT(CASE WHEN mr.status = 'completed' THEN 1 END) as completed_requests,
        COUNT(CASE WHEN mr.priority = 'urgent' THEN 1 END) as urgent_requests,
        COUNT(CASE WHEN mr.priority = 'high' THEN 1 END) as high_priority_requests,
        AVG(CASE 
          WHEN mr.status = 'completed' AND mr.completed_at IS NOT NULL 
          THEN DATEDIFF(mr.completed_at, mr.created_at) 
        END) as average_completion_days,
        COALESCE(SUM(mr.estimated_cost), 0) as total_estimated_cost,
        COALESCE(SUM(mr.actual_cost), 0) as total_actual_cost
      FROM maintenance_requests mr
      JOIN units u ON mr.unit_id = u.id
      JOIN properties p ON u.property_id = p.id
      ${whereClause}
    `;
    
    const results = await executeQuery(query, params);
    return results[0];
  }

  // Export data for external reporting
  static async exportData(
    reportType: string,
    landlordId?: string,
    dateFrom?: Date,
    dateTo?: Date,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> {
    let data: any;
    
    switch (reportType) {
      case 'financial':
        data = await this.getFinancialReport(landlordId, dateFrom, dateTo);
        break;
      case 'properties':
        data = await this.getPropertyPerformanceReport(landlordId);
        break;
      case 'tenants':
        data = await this.getTenantPerformanceReport(landlordId);
        break;
      case 'occupancy':
        data = await this.getOccupancyReport(landlordId);
        break;
      case 'revenue':
        data = await this.getRevenueTrendReport(landlordId);
        break;
      case 'maintenance':
        data = await this.getMaintenanceReport(landlordId);
        break;
      default:
        throw new Error('Invalid report type');
    }
    
    if (format === 'csv') {
      // Convert to CSV format (simplified implementation)
      return this.convertToCSV(data);
    }
    
    return data;
  }

  // Helper method to convert data to CSV
  private static convertToCSV(data: any): string {
    if (Array.isArray(data)) {
      if (data.length === 0) return '';
      
      const headers = Object.keys(data[0]);
      const csvHeaders = headers.join(',');
      const csvRows = data.map(row => 
        headers.map(header => `"${row[header] || ''}"`).join(',')
      );
      
      return [csvHeaders, ...csvRows].join('\n');
    } else {
      // Single object - convert to key-value CSV
      const entries = Object.entries(data);
      return entries.map(([key, value]) => `"${key}","${value}"`).join('\n');
    }
  }
}
