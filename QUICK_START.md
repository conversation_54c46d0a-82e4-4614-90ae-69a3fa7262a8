# PropertyPro Quick Start Guide

## ✅ Issues Fixed

1. **JSX Syntax Error**: Fixed by renaming `useAuth.ts` to `useAuth.tsx`
2. **Login Function**: Updated to handle Promise-based authentication
3. **JWT Configuration**: Secure secrets properly configured
4. **Database Connection**: Tested and working

## 🚀 Quick Start Instructions

### Option 1: Using the Batch Script (Windows)
1. **Double-click** `start-dev.bat` in the project root
2. This will open two command windows:
   - Backend server (port 5000)
   - Frontend server (port 5173)

### Option 2: Manual Start
1. **Start Backend** (in one terminal):
   ```bash
   cd server
   npm run dev
   ```

2. **Start Frontend** (in another terminal):
   ```bash
   npm run dev
   ```

### Option 3: Database Setup (if not done yet)
1. **Open MySQL Workbench or command line**
2. **Create database**:
   ```sql
   CREATE DATABASE IF NOT EXISTS property_management;
   USE property_management;
   ```
3. **Import schema**: Copy content from `supabase/migrations/20250710060353_old_dream.sql` and execute

## 🔐 Test Login Credentials

Once the servers are running, you can test with these steps:

1. **Open**: http://localhost:5173
2. **Register a new user** or **use the API** to create one:
   ```bash
   POST http://localhost:5000/api/v1/auth/register
   {
     "email": "<EMAIL>",
     "password": "password123",
     "first_name": "Admin",
     "last_name": "User",
     "role": "landlord"
   }
   ```
3. **Login** with the credentials you created

## 🧪 API Testing

Run the API test script:
```bash
node test-api.js
```

Expected output:
```
🚀 Testing PropertyPro API Endpoints...
✅ Health check: OK
✅ User registration successful
✅ Login successful
✅ Protected endpoint access successful
✅ Dashboard stats retrieved
✅ Properties endpoint working
```

## 📱 Mobile Testing

Test responsive design by:
1. Opening browser developer tools (F12)
2. Clicking the device toggle button
3. Testing different screen sizes:
   - iPhone SE (375x667)
   - iPad (768x1024)
   - Desktop (1920x1080)

## 🔧 Troubleshooting

### Frontend won't start
- Check if port 5173 is available
- Run `npm install` to ensure dependencies are installed

### Backend won't start
- Check if port 5000 is available
- Verify MySQL is running
- Check database credentials in `server/.env`

### Database connection issues
- Ensure MySQL server is running
- Verify database `property_management` exists
- Check credentials in `server/.env`

### API calls failing
- Ensure backend is running on port 5000
- Check browser console for CORS errors
- Verify `.env.local` has correct API URL

## ✅ Success Indicators

You'll know everything is working when:
- ✅ Backend shows: "PropertyPro Backend Server running on port 5000"
- ✅ Frontend shows: "Local: http://localhost:5173/"
- ✅ You can access the login page at http://localhost:5173
- ✅ API test script shows all green checkmarks
- ✅ You can register and login successfully

## 🎯 Next Steps

After successful testing:
1. Create your first property
2. Add units to the property
3. Test the mobile responsive design
4. Explore the dashboard analytics
5. Test maintenance request workflow

---

**Need Help?** Check the console logs for detailed error messages and refer to `TESTING_GUIDE.md` for comprehensive troubleshooting.
