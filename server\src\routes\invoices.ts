import { Router, Request, Response } from 'express';
import { InvoiceModel } from '../models/Invoice.js';
import { LeaseModel } from '../models/Lease.js';
import { TenantModel } from '../models/Tenant.js';
import { authorizeRoles } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { Invoice } from '../types/index.js';

const router = Router();

// Get all invoices
router.get('/',
  authorizeRoles('admin', 'landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      propertyId,
      status,
      invoiceType,
      overdue
    } = req.query as any;

    const options: any = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      propertyId,
      status,
      invoiceType,
      overdue: overdue === 'true'
    };

    // Filter based on user role
    if (req.user!.role === 'landlord') {
      options.landlordId = req.user!.id;
    } else if (req.user!.role === 'tenant') {
      const tenant = await TenantModel.findByUserId(req.user!.id);
      if (tenant) {
        options.tenantId = tenant.id;
      }
    }

    const { invoices, total } = await InvoiceModel.findAll(options);

    res.json({
      success: true,
      data: invoices,
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages: Math.ceil(total / options.limit)
      }
    });
  })
);

// Get invoice by ID
router.get('/:id',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const invoice = await InvoiceModel.findById(id);

    if (!invoice) {
      throw new AppError('Invoice not found', 404);
    }

    // Check permissions
    if (req.user!.role === 'tenant') {
      const tenant = await TenantModel.findByUserId(req.user!.id);
      if (!tenant || tenant.id !== invoice.tenant_id) {
        throw new AppError('Access denied', 403);
      }
    }

    res.json({
      success: true,
      data: invoice
    });
  })
);

// Create new invoice
router.post('/',
  authorizeRoles('admin', 'landlord', 'property_manager'),
  validate(schemas.createInvoice),
  asyncHandler(async (req: Request, res: Response) => {
    const invoiceData: Partial<Invoice> = req.body;

    // Validate lease exists if provided
    if (invoiceData.lease_id) {
      const lease = await LeaseModel.findById(invoiceData.lease_id);
      if (!lease) {
        throw new AppError('Lease not found', 404);
      }

      // Auto-set tenant_id from lease if not provided
      if (!invoiceData.tenant_id) {
        invoiceData.tenant_id = lease.tenant_id;
      }
    }

    // Validate tenant exists
    if (invoiceData.tenant_id) {
      const tenant = await TenantModel.findById(invoiceData.tenant_id);
      if (!tenant) {
        throw new AppError('Tenant not found', 404);
      }
    }

    const invoice = await InvoiceModel.create(invoiceData);

    res.status(201).json({
      success: true,
      message: 'Invoice created successfully',
      data: invoice
    });
  })
);

// Update invoice
router.put('/:id',
  authorizeRoles('admin', 'landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData: Partial<Invoice> = req.body;

    // Check if invoice exists
    const existingInvoice = await InvoiceModel.findById(id);
    if (!existingInvoice) {
      throw new AppError('Invoice not found', 404);
    }

    // Don't allow updating paid invoices
    if (existingInvoice.status === 'paid') {
      throw new AppError('Cannot update paid invoices', 400);
    }

    const updatedInvoice = await InvoiceModel.update(id, updateData);

    res.json({
      success: true,
      message: 'Invoice updated successfully',
      data: updatedInvoice
    });
  })
);

// Send invoice
router.post('/:id/send',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const invoice = await InvoiceModel.findById(id);
    if (!invoice) {
      throw new AppError('Invoice not found', 404);
    }

    if (invoice.status !== 'draft') {
      throw new AppError('Only draft invoices can be sent', 400);
    }

    const sentInvoice = await InvoiceModel.markAsSent(id);

    // Here you would typically send email notification to tenant
    // await emailService.sendInvoice(invoice);

    res.json({
      success: true,
      message: 'Invoice sent successfully',
      data: sentInvoice
    });
  })
);

// Mark invoice as viewed (typically called when tenant views invoice)
router.post('/:id/view',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const invoice = await InvoiceModel.findById(id);
    if (!invoice) {
      throw new AppError('Invoice not found', 404);
    }

    // Check permissions for tenant
    if (req.user!.role === 'tenant') {
      const tenant = await TenantModel.findByUserId(req.user!.id);
      if (!tenant || tenant.id !== invoice.tenant_id) {
        throw new AppError('Access denied', 403);
      }
    }

    if (invoice.status === 'sent') {
      const viewedInvoice = await InvoiceModel.markAsViewed(id);

      res.json({
        success: true,
        message: 'Invoice marked as viewed',
        data: viewedInvoice
      });
    } else {
      res.json({
        success: true,
        message: 'Invoice status unchanged',
        data: invoice
      });
    }
  })
);

// Apply late fee
router.post('/:id/late-fee',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      throw new AppError('Valid late fee amount is required', 400);
    }

    const invoice = await InvoiceModel.findById(id);
    if (!invoice) {
      throw new AppError('Invoice not found', 404);
    }

    if (invoice.status === 'paid') {
      throw new AppError('Cannot apply late fee to paid invoice', 400);
    }

    if (invoice.late_fee_applied) {
      throw new AppError('Late fee already applied to this invoice', 400);
    }

    const updatedInvoice = await InvoiceModel.applyLateFee(id, amount);

    res.json({
      success: true,
      message: 'Late fee applied successfully',
      data: updatedInvoice
    });
  })
);

// Get overdue invoices
router.get('/overdue/list',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    const overdueInvoices = await InvoiceModel.getOverdueInvoices(landlordId);

    res.json({
      success: true,
      data: overdueInvoices
    });
  })
);

// Generate recurring invoices (monthly rent)
router.post('/generate-recurring',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const generatedCount = await InvoiceModel.generateRecurringInvoices();

    res.json({
      success: true,
      message: `Generated ${generatedCount} recurring invoices`,
      data: { count: generatedCount }
    });
  })
);

// Get invoice statistics
router.get('/stats/summary',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    const stats = await InvoiceModel.getStats(landlordId);

    res.json({
      success: true,
      data: stats
    });
  })
);

export default router;
