import { executeQuery, executeTransaction } from '../config/database';
import { Property, Unit, PropertyStats } from '../types';

export class PropertyService {
  // Create new property
  static async createProperty(propertyData: Omit<Property, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const query = `
      INSERT INTO properties (name, description, address, city, state, postal_code, 
                            country, property_type, landlord_id, manager_id, images, amenities)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      propertyData.name,
      propertyData.description || null,
      propertyData.address,
      propertyData.city,
      propertyData.state || null,
      propertyData.postalCode || null,
      propertyData.country || 'Kenya',
      propertyData.type,
      propertyData.landlordId,
      propertyData.managerId || null,
      JSON.stringify(propertyData.images || []),
      JSON.stringify(propertyData.amenities || [])
    ];
    
    const result = await executeQuery(query, params);
    return result.insertId;
  }

  // Get properties by landlord
  static async getPropertiesByLandlord(landlordId: string): Promise<Property[]> {
    const query = `
      SELECT p.*, 
             COUNT(u.id) as total_units,
             COUNT(CASE WHEN u.is_occupied = TRUE THEN 1 END) as occupied_units
      FROM properties p
      LEFT JOIN units u ON p.id = u.id
      WHERE p.landlord_id = ? AND p.is_active = TRUE
      GROUP BY p.id
      ORDER BY p.created_at DESC
    `;
    
    const results = await executeQuery(query, [landlordId]);
    return results.map(this.mapDatabaseToProperty);
  }

  // Get properties by manager
  static async getPropertiesByManager(managerId: string): Promise<Property[]> {
    const query = `
      SELECT p.*, 
             COUNT(u.id) as total_units,
             COUNT(CASE WHEN u.is_occupied = TRUE THEN 1 END) as occupied_units
      FROM properties p
      LEFT JOIN units u ON p.id = u.id
      LEFT JOIN property_assignments pa ON p.id = pa.property_id
      WHERE (p.manager_id = ? OR (pa.user_id = ? AND pa.role = 'manager' AND pa.is_active = TRUE))
        AND p.is_active = TRUE
      GROUP BY p.id
      ORDER BY p.created_at DESC
    `;
    
    const results = await executeQuery(query, [managerId, managerId]);
    return results.map(this.mapDatabaseToProperty);
  }

  // Get property statistics
  static async getPropertyStats(propertyId: string): Promise<PropertyStats> {
    const query = `
      SELECT 
        COUNT(u.id) as total_units,
        COUNT(CASE WHEN u.is_occupied = TRUE THEN 1 END) as occupied_units,
        COUNT(CASE WHEN u.is_occupied = FALSE THEN 1 END) as vacant_units,
        SUM(u.monthly_rent) as potential_revenue,
        SUM(CASE WHEN u.is_occupied = TRUE THEN u.monthly_rent ELSE 0 END) as actual_revenue,
        AVG(u.monthly_rent) as average_rent
      FROM units u
      WHERE u.property_id = ?
    `;
    
    const [result] = await executeQuery(query, [propertyId]);
    
    return {
      totalUnits: result.total_units || 0,
      occupiedUnits: result.occupied_units || 0,
      vacantUnits: result.vacant_units || 0,
      occupancyRate: result.total_units > 0 ? (result.occupied_units / result.total_units) * 100 : 0,
      potentialRevenue: parseFloat(result.potential_revenue || '0'),
      actualRevenue: parseFloat(result.actual_revenue || '0'),
      averageRent: parseFloat(result.average_rent || '0')
    };
  }

  // Create unit
  static async createUnit(unitData: Omit<Unit, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const query = `
      INSERT INTO units (property_id, unit_number, unit_type, floor_number, square_feet,
                        bedrooms, bathrooms, monthly_rent, security_deposit, description,
                        amenities, images)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      unitData.propertyId,
      unitData.unitNumber,
      unitData.type,
      unitData.floorNumber || null,
      unitData.squareFeet || null,
      unitData.bedrooms || 0,
      unitData.bathrooms || 0,
      unitData.rent,
      unitData.deposit,
      unitData.description || null,
      JSON.stringify(unitData.amenities || []),
      JSON.stringify(unitData.images || [])
    ];
    
    const result = await executeQuery(query, params);
    
    // Update property total units count
    await this.updatePropertyUnitCount(unitData.propertyId);
    
    return result.insertId;
  }

  // Get units by property
  static async getUnitsByProperty(propertyId: string): Promise<Unit[]> {
    const query = `
      SELECT u.*, 
             t.id as tenant_id,
             CONCAT(usr.first_name, ' ', usr.last_name) as tenant_name,
             usr.email as tenant_email,
             usr.phone as tenant_phone,
             l.start_date as lease_start,
             l.end_date as lease_end,
             l.status as lease_status
      FROM units u
      LEFT JOIN leases l ON u.id = l.unit_id AND l.status = 'active'
      LEFT JOIN tenants t ON l.tenant_id = t.id
      LEFT JOIN users usr ON t.user_id = usr.id
      WHERE u.property_id = ?
      ORDER BY u.unit_number
    `;
    
    const results = await executeQuery(query, [propertyId]);
    return results.map(this.mapDatabaseToUnit);
  }

  // Update property unit count
  private static async updatePropertyUnitCount(propertyId: string): Promise<void> {
    const query = `
      UPDATE properties 
      SET total_units = (
        SELECT COUNT(*) FROM units WHERE property_id = ?
      )
      WHERE id = ?
    `;
    
    await executeQuery(query, [propertyId, propertyId]);
  }

  // Map database result to Property object
  private static mapDatabaseToProperty(row: any): Property {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      address: row.address,
      city: row.city,
      state: row.state,
      postalCode: row.postal_code,
      country: row.country,
      type: row.property_type,
      totalUnits: row.total_units || 0,
      landlordId: row.landlord_id,
      managerId: row.manager_id,
      images: row.images ? JSON.parse(row.images) : [],
      amenities: row.amenities ? JSON.parse(row.amenities) : [],
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  // Map database result to Unit object
  private static mapDatabaseToUnit(row: any): Unit {
    return {
      id: row.id,
      propertyId: row.property_id,
      unitNumber: row.unit_number,
      type: row.unit_type,
      floorNumber: row.floor_number,
      squareFeet: row.square_feet,
      bedrooms: row.bedrooms,
      bathrooms: row.bathrooms,
      rent: parseFloat(row.monthly_rent),
      deposit: parseFloat(row.security_deposit),
      description: row.description,
      amenities: row.amenities ? JSON.parse(row.amenities) : [],
      images: row.images ? JSON.parse(row.images) : [],
      isOccupied: row.is_occupied,
      isAvailable: row.is_available,
      tenant: row.tenant_id ? {
        id: row.tenant_id,
        name: row.tenant_name,
        email: row.tenant_email,
        phone: row.tenant_phone,
        emergencyContact: ''
      } : undefined,
      lease: row.lease_start ? {
        startDate: row.lease_start,
        endDate: row.lease_end,
        status: row.lease_status
      } : undefined,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }
}