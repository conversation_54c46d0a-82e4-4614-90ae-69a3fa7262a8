import { Router, Request, Response } from 'express';
import { LeaseModel } from '../models/Lease.js';
import { TenantModel } from '../models/Tenant.js';
import { UnitModel } from '../models/Unit.js';
import { authorizeRoles } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { Lease } from '../types/index.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for lease document uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(process.cwd(), 'uploads', 'leases');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `lease-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = file.mimetype === 'application/pdf' ||
                     file.mimetype === 'application/msword' ||
                     file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only PDF and Word documents are allowed'));
    }
  }
});

// Get all leases
router.get('/',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      propertyId,
      status,
      expiringWithin
    } = req.query as any;

    const options: any = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      propertyId,
      status,
      expiringWithin: expiringWithin ? parseInt(expiringWithin) : undefined
    };

    // Filter based on user role
    if (req.user!.role === 'landlord') {
      options.landlordId = req.user!.id;
    } else if (req.user!.role === 'tenant') {
      const tenant = await TenantModel.findByUserId(req.user!.id);
      if (tenant) {
        options.tenantId = tenant.id;
      }
    }

    const { leases, total } = await LeaseModel.findAll(options);

    res.json({
      success: true,
      data: leases,
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages: Math.ceil(total / options.limit)
      }
    });
  })
);

// Get lease by ID
router.get('/:id',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const lease = await LeaseModel.findById(id);

    if (!lease) {
      throw new AppError('Lease not found', 404);
    }

    // Check permissions
    if (req.user!.role === 'tenant') {
      const tenant = await TenantModel.findByUserId(req.user!.id);
      if (!tenant || tenant.id !== lease.tenant_id) {
        throw new AppError('Access denied', 403);
      }
    }

    res.json({
      success: true,
      data: lease
    });
  })
);

// Create new lease
router.post('/',
  authorizeRoles('landlord', 'property_manager'),
  upload.single('lease_document'),
  validate(schemas.createLease),
  asyncHandler(async (req: Request, res: Response) => {
    const leaseData: Partial<Lease> = req.body;

    // Handle uploaded document
    const file = req.file;
    if (file) {
      leaseData.lease_document_url = `/uploads/leases/${file.filename}`;
    }

    // Validate unit exists and is available
    const unit = await UnitModel.findById(leaseData.unit_id!);
    if (!unit) {
      throw new AppError('Unit not found', 404);
    }

    if (unit.is_occupied && leaseData.status === 'active') {
      throw new AppError('Unit is already occupied', 400);
    }

    // Validate tenant exists
    const tenant = await TenantModel.findById(leaseData.tenant_id!);
    if (!tenant) {
      throw new AppError('Tenant not found', 404);
    }

    // Check for lease conflicts
    const hasConflicts = await LeaseModel.checkConflicts(
      leaseData.unit_id!,
      new Date(leaseData.start_date!),
      new Date(leaseData.end_date!)
    );

    if (hasConflicts) {
      throw new AppError('Lease dates conflict with existing lease for this unit', 409);
    }

    const lease = await LeaseModel.create(leaseData);

    res.status(201).json({
      success: true,
      message: 'Lease created successfully',
      data: lease
    });
  })
);

// Update lease
router.put('/:id',
  authorizeRoles('landlord', 'property_manager'),
  upload.single('lease_document'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData: Partial<Lease> = req.body;

    // Check if lease exists
    const existingLease = await LeaseModel.findById(id);
    if (!existingLease) {
      throw new AppError('Lease not found', 404);
    }

    // Handle uploaded document
    const file = req.file;
    if (file) {
      updateData.lease_document_url = `/uploads/leases/${file.filename}`;
    }

    // Check for conflicts if dates are being updated
    if (updateData.start_date || updateData.end_date) {
      const startDate = updateData.start_date ? new Date(updateData.start_date) : new Date(existingLease.start_date);
      const endDate = updateData.end_date ? new Date(updateData.end_date) : new Date(existingLease.end_date);

      const hasConflicts = await LeaseModel.checkConflicts(
        existingLease.unit_id,
        startDate,
        endDate,
        id
      );

      if (hasConflicts) {
        throw new AppError('Updated lease dates conflict with existing lease for this unit', 409);
      }
    }

    const updatedLease = await LeaseModel.update(id, updateData);

    res.json({
      success: true,
      message: 'Lease updated successfully',
      data: updatedLease
    });
  })
);

// Terminate lease
router.post('/:id/terminate',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { reason } = req.body;

    if (!reason) {
      throw new AppError('Termination reason is required', 400);
    }

    const lease = await LeaseModel.findById(id);
    if (!lease) {
      throw new AppError('Lease not found', 404);
    }

    if (lease.status === 'terminated') {
      throw new AppError('Lease is already terminated', 400);
    }

    const terminatedLease = await LeaseModel.terminate(id, reason);

    res.json({
      success: true,
      message: 'Lease terminated successfully',
      data: terminatedLease
    });
  })
);

// Renew lease
router.post('/:id/renew',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { new_end_date, new_monthly_rent, special_terms } = req.body;

    if (!new_end_date) {
      throw new AppError('New end date is required', 400);
    }

    const lease = await LeaseModel.findById(id);
    if (!lease) {
      throw new AppError('Lease not found', 404);
    }

    if (lease.status !== 'active') {
      throw new AppError('Only active leases can be renewed', 400);
    }

    const renewedLease = await LeaseModel.renew(id, {
      new_end_date: new Date(new_end_date),
      new_monthly_rent,
      special_terms
    });

    res.json({
      success: true,
      message: 'Lease renewed successfully',
      data: renewedLease
    });
  })
);

// Get expiring leases
router.get('/expiring/:days',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { days } = req.params;
    const daysNumber = parseInt(days);

    if (isNaN(daysNumber) || daysNumber < 1) {
      throw new AppError('Days must be a positive number', 400);
    }

    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    const expiringLeases = await LeaseModel.getExpiringLeases(daysNumber, landlordId);

    res.json({
      success: true,
      data: expiringLeases
    });
  })
);

export default router;
