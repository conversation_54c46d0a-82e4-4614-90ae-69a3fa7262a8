-- Property Management System Database Schema
-- MySQL Database Schema for Enterprise Property Management Solution

-- Create database
CREATE DATABASE IF NOT EXISTS property_management;
USE property_management;

-- Users table (supports all user roles)
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('admin', 'landlord', 'property_manager', 'caretaker', 'tenant') NOT NULL,
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- Properties table
CREATE TABLE properties (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Kenya',
    property_type ENUM('residential', 'commercial', 'mixed') NOT NULL,
    total_units INT DEFAULT 0,
    landlord_id VARCHAR(36) NOT NULL,
    manager_id VARCHAR(36),
    images JSON, -- Store array of image URLs
    amenities JSON, -- Store array of amenities
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (landlord_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_landlord (landlord_id),
    INDEX idx_manager (manager_id),
    INDEX idx_type (property_type),
    INDEX idx_city (city)
);

-- Units table
CREATE TABLE units (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    property_id VARCHAR(36) NOT NULL,
    unit_number VARCHAR(50) NOT NULL,
    unit_type ENUM('studio', '1br', '2br', '3br', '4br', 'office', 'retail', 'warehouse') NOT NULL,
    floor_number INT,
    square_feet DECIMAL(10,2),
    bedrooms INT DEFAULT 0,
    bathrooms DECIMAL(3,1) DEFAULT 0,
    monthly_rent DECIMAL(10,2) NOT NULL,
    security_deposit DECIMAL(10,2) NOT NULL,
    description TEXT,
    amenities JSON,
    images JSON,
    is_occupied BOOLEAN DEFAULT FALSE,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    UNIQUE KEY unique_unit_per_property (property_id, unit_number),
    INDEX idx_property (property_id),
    INDEX idx_occupied (is_occupied),
    INDEX idx_available (is_available),
    INDEX idx_rent (monthly_rent)
);

-- Tenants table (extended user information for tenants)
CREATE TABLE tenants (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    employer VARCHAR(255),
    monthly_income DECIMAL(10,2),
    id_number VARCHAR(50),
    id_document_url VARCHAR(500),
    background_check_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    background_check_date TIMESTAMP NULL,
    move_in_date DATE,
    move_out_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_background_status (background_check_status)
);

-- Leases table
CREATE TABLE leases (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    unit_id VARCHAR(36) NOT NULL,
    tenant_id VARCHAR(36) NOT NULL,
    lease_type ENUM('fixed', 'month_to_month', 'commercial') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    monthly_rent DECIMAL(10,2) NOT NULL,
    security_deposit DECIMAL(10,2) NOT NULL,
    late_fee_amount DECIMAL(10,2) DEFAULT 0,
    late_fee_type ENUM('flat', 'percentage') DEFAULT 'flat',
    grace_period_days INT DEFAULT 5,
    lease_document_url VARCHAR(500),
    status ENUM('draft', 'active', 'expired', 'terminated', 'renewed') DEFAULT 'draft',
    termination_date DATE NULL,
    termination_reason TEXT,
    auto_renew BOOLEAN DEFAULT FALSE,
    renewal_notice_days INT DEFAULT 30,
    special_terms TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE CASCADE,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_unit (unit_id),
    INDEX idx_tenant (tenant_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_expiring (end_date, status)
);

-- Invoices table
CREATE TABLE invoices (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    lease_id VARCHAR(36) NOT NULL,
    tenant_id VARCHAR(36) NOT NULL,
    invoice_type ENUM('rent', 'deposit', 'late_fee', 'utility', 'maintenance', 'other') DEFAULT 'rent',
    amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    due_date DATE NOT NULL,
    issue_date DATE NOT NULL,
    description TEXT,
    line_items JSON, -- Store detailed breakdown
    status ENUM('draft', 'sent', 'viewed', 'paid', 'partial', 'overdue', 'cancelled') DEFAULT 'draft',
    paid_amount DECIMAL(10,2) DEFAULT 0,
    payment_date TIMESTAMP NULL,
    late_fee_applied DECIMAL(10,2) DEFAULT 0,
    pdf_url VARCHAR(500),
    reminder_sent_count INT DEFAULT 0,
    last_reminder_sent TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (lease_id) REFERENCES leases(id) ON DELETE CASCADE,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_lease (lease_id),
    INDEX idx_tenant (tenant_id),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_overdue (due_date, status)
);

-- Payments table
CREATE TABLE payments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    invoice_id VARCHAR(36) NOT NULL,
    tenant_id VARCHAR(36) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('mpesa', 'bank_transfer', 'card', 'cash', 'cheque') NOT NULL,
    transaction_reference VARCHAR(255),
    mpesa_receipt_number VARCHAR(100),
    payment_date TIMESTAMP NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    gateway_response JSON, -- Store payment gateway response
    reconciliation_status ENUM('pending', 'matched', 'unmatched') DEFAULT 'pending',
    reconciled_at TIMESTAMP NULL,
    reconciled_by VARCHAR(36),
    fees DECIMAL(10,2) DEFAULT 0,
    net_amount DECIMAL(10,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (reconciled_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_invoice (invoice_id),
    INDEX idx_tenant (tenant_id),
    INDEX idx_status (status),
    INDEX idx_payment_date (payment_date),
    INDEX idx_transaction_ref (transaction_reference),
    INDEX idx_reconciliation (reconciliation_status)
);

-- Maintenance requests table
CREATE TABLE maintenance_requests (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    request_number VARCHAR(50) UNIQUE NOT NULL,
    unit_id VARCHAR(36) NOT NULL,
    tenant_id VARCHAR(36),
    reported_by VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category ENUM('plumbing', 'electrical', 'hvac', 'appliance', 'structural', 'pest_control', 'cleaning', 'security', 'other') NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent', 'emergency') NOT NULL,
    status ENUM('submitted', 'acknowledged', 'assigned', 'in_progress', 'completed', 'cancelled', 'on_hold') DEFAULT 'submitted',
    assigned_to VARCHAR(36),
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    scheduled_date TIMESTAMP NULL,
    completed_date TIMESTAMP NULL,
    images JSON, -- Store array of image URLs
    work_notes TEXT,
    tenant_satisfaction_rating INT CHECK (tenant_satisfaction_rating BETWEEN 1 AND 5),
    tenant_feedback TEXT,
    is_emergency BOOLEAN DEFAULT FALSE,
    requires_tenant_access BOOLEAN DEFAULT TRUE,
    preferred_contact_method ENUM('phone', 'email', 'sms') DEFAULT 'phone',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE CASCADE,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL,
    FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_unit (unit_id),
    INDEX idx_tenant (tenant_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_assigned (assigned_to),
    INDEX idx_category (category),
    INDEX idx_emergency (is_emergency),
    INDEX idx_request_number (request_number)
);

-- Maintenance work orders table
CREATE TABLE work_orders (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    maintenance_request_id VARCHAR(36) NOT NULL,
    work_order_number VARCHAR(50) UNIQUE NOT NULL,
    assigned_to VARCHAR(36) NOT NULL,
    contractor_name VARCHAR(255),
    contractor_phone VARCHAR(20),
    contractor_email VARCHAR(255),
    scheduled_start TIMESTAMP,
    scheduled_end TIMESTAMP,
    actual_start TIMESTAMP,
    actual_end TIMESTAMP,
    materials_needed TEXT,
    materials_cost DECIMAL(10,2) DEFAULT 0,
    labor_cost DECIMAL(10,2) DEFAULT 0,
    total_cost DECIMAL(10,2) DEFAULT 0,
    work_description TEXT,
    completion_notes TEXT,
    before_images JSON,
    after_images JSON,
    status ENUM('created', 'scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'created',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_maintenance_request (maintenance_request_id),
    INDEX idx_assigned (assigned_to),
    INDEX idx_status (status),
    INDEX idx_scheduled (scheduled_start, scheduled_end)
);

-- Property assignments (for property managers)
CREATE TABLE property_assignments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    property_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    role ENUM('manager', 'caretaker') NOT NULL,
    assigned_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_assignment (property_id, user_id, role),
    INDEX idx_property (property_id),
    INDEX idx_user (user_id),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- Notifications table
CREATE TABLE notifications (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
    category ENUM('payment', 'maintenance', 'lease', 'system', 'reminder') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500),
    metadata JSON,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_unread (user_id, is_read),
    INDEX idx_category (category),
    INDEX idx_created (created_at)
);

-- Audit log table
CREATE TABLE audit_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(36),
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_table (table_name),
    INDEX idx_record (record_id),
    INDEX idx_created (created_at)
);

-- System settings table
CREATE TABLE system_settings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- Email templates table
CREATE TABLE email_templates (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    template_name VARCHAR(100) UNIQUE NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body_html TEXT NOT NULL,
    body_text TEXT,
    variables JSON, -- Store available template variables
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (template_name),
    INDEX idx_active (is_active)
);

-- SMS templates table
CREATE TABLE sms_templates (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    template_name VARCHAR(100) UNIQUE NOT NULL,
    message TEXT NOT NULL,
    variables JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (template_name),
    INDEX idx_active (is_active)
);

-- Communication log table
CREATE TABLE communication_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    recipient_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36),
    communication_type ENUM('email', 'sms', 'push', 'in_app') NOT NULL,
    template_used VARCHAR(100),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status ENUM('pending', 'sent', 'delivered', 'failed', 'bounced') DEFAULT 'pending',
    external_id VARCHAR(255), -- ID from email/SMS service
    error_message TEXT,
    metadata JSON,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_recipient (recipient_id),
    INDEX idx_sender (sender_id),
    INDEX idx_type (communication_type),
    INDEX idx_status (status),
    INDEX idx_sent (sent_at)
);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('company_name', 'PropertyPro Enterprise', 'string', 'Company name displayed in the application', TRUE),
('company_email', '<EMAIL>', 'string', 'Default company email address', FALSE),
('company_phone', '+254700000000', 'string', 'Company phone number', TRUE),
('default_currency', 'KES', 'string', 'Default currency for the system', TRUE),
('late_fee_grace_days', '5', 'number', 'Default grace period for late fees', FALSE),
('default_late_fee_percentage', '5', 'number', 'Default late fee percentage', FALSE),
('mpesa_paybill', '123456', 'string', 'M-Pesa paybill number', TRUE),
('email_notifications_enabled', 'true', 'boolean', 'Enable email notifications', FALSE),
('sms_notifications_enabled', 'true', 'boolean', 'Enable SMS notifications', FALSE),
('auto_generate_invoices', 'true', 'boolean', 'Automatically generate monthly rent invoices', FALSE);

-- Insert default email templates
INSERT INTO email_templates (template_name, subject, body_html, body_text, variables) VALUES
('rent_reminder', 'Rent Payment Reminder - {{property_name}} Unit {{unit_number}}', 
'<h2>Rent Payment Reminder</h2><p>Dear {{tenant_name}},</p><p>This is a friendly reminder that your rent payment of <strong>{{amount}}</strong> for {{property_name}} Unit {{unit_number}} is due on {{due_date}}.</p><p>Please make your payment through:</p><ul><li>M-Pesa Paybill: {{mpesa_paybill}}</li><li>Bank Transfer</li><li>Online Portal</li></ul><p>Thank you!</p>',
'Rent Payment Reminder\n\nDear {{tenant_name}},\n\nThis is a friendly reminder that your rent payment of {{amount}} for {{property_name}} Unit {{unit_number}} is due on {{due_date}}.\n\nPlease make your payment through M-Pesa Paybill: {{mpesa_paybill}} or our online portal.\n\nThank you!',
'["tenant_name", "property_name", "unit_number", "amount", "due_date", "mpesa_paybill"]'),

('payment_confirmation', 'Payment Received - {{property_name}} Unit {{unit_number}}',
'<h2>Payment Confirmation</h2><p>Dear {{tenant_name}},</p><p>We have received your payment of <strong>{{amount}}</strong> for {{property_name}} Unit {{unit_number}}.</p><p>Transaction Reference: {{transaction_ref}}</p><p>Payment Date: {{payment_date}}</p><p>Thank you for your prompt payment!</p>',
'Payment Confirmation\n\nDear {{tenant_name}},\n\nWe have received your payment of {{amount}} for {{property_name}} Unit {{unit_number}}.\n\nTransaction Reference: {{transaction_ref}}\nPayment Date: {{payment_date}}\n\nThank you for your prompt payment!',
'["tenant_name", "property_name", "unit_number", "amount", "transaction_ref", "payment_date"]');

-- Insert default SMS templates
INSERT INTO sms_templates (template_name, message, variables) VALUES
('rent_reminder_sms', 'Hi {{tenant_name}}, your rent of {{amount}} for Unit {{unit_number}} is due on {{due_date}}. Pay via M-Pesa {{mpesa_paybill}}. Thank you!',
'["tenant_name", "amount", "unit_number", "due_date", "mpesa_paybill"]'),

('payment_confirmation_sms', 'Payment of {{amount}} received for Unit {{unit_number}}. Ref: {{transaction_ref}}. Thank you {{tenant_name}}!',
'["tenant_name", "amount", "unit_number", "transaction_ref"]');

-- Create indexes for better performance
CREATE INDEX idx_users_email_active ON users(email, is_active);
CREATE INDEX idx_properties_landlord_active ON properties(landlord_id, is_active);
CREATE INDEX idx_units_property_available ON units(property_id, is_available);
CREATE INDEX idx_leases_unit_status ON leases(unit_id, status);
CREATE INDEX idx_invoices_tenant_status ON invoices(tenant_id, status);
CREATE INDEX idx_payments_invoice_status ON payments(invoice_id, status);
CREATE INDEX idx_maintenance_unit_status ON maintenance_requests(unit_id, status);

-- Create views for common queries
CREATE VIEW active_leases AS
SELECT 
    l.*,
    u.unit_number,
    u.monthly_rent as current_rent,
    p.name as property_name,
    p.address as property_address,
    CONCAT(usr.first_name, ' ', usr.last_name) as tenant_name,
    usr.email as tenant_email,
    usr.phone as tenant_phone
FROM leases l
JOIN units u ON l.unit_id = u.id
JOIN properties p ON u.property_id = p.id
JOIN tenants t ON l.tenant_id = t.id
JOIN users usr ON t.user_id = usr.id
WHERE l.status = 'active';

CREATE VIEW property_occupancy AS
SELECT 
    p.id as property_id,
    p.name as property_name,
    COUNT(u.id) as total_units,
    COUNT(CASE WHEN u.is_occupied = TRUE THEN 1 END) as occupied_units,
    COUNT(CASE WHEN u.is_occupied = FALSE THEN 1 END) as vacant_units,
    ROUND((COUNT(CASE WHEN u.is_occupied = TRUE THEN 1 END) / COUNT(u.id)) * 100, 2) as occupancy_rate,
    SUM(u.monthly_rent) as potential_revenue,
    SUM(CASE WHEN u.is_occupied = TRUE THEN u.monthly_rent ELSE 0 END) as actual_revenue
FROM properties p
LEFT JOIN units u ON p.id = u.property_id
WHERE p.is_active = TRUE
GROUP BY p.id, p.name;

CREATE VIEW overdue_invoices AS
SELECT 
    i.*,
    u.unit_number,
    p.name as property_name,
    CONCAT(usr.first_name, ' ', usr.last_name) as tenant_name,
    usr.email as tenant_email,
    usr.phone as tenant_phone,
    DATEDIFF(CURDATE(), i.due_date) as days_overdue
FROM invoices i
JOIN leases l ON i.lease_id = l.id
JOIN units u ON l.unit_id = u.id
JOIN properties p ON u.property_id = p.id
JOIN tenants t ON i.tenant_id = t.id
JOIN users usr ON t.user_id = usr.id
WHERE i.status IN ('overdue', 'partial') 
AND i.due_date < CURDATE();