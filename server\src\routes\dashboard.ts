import { Router, Request, Response } from 'express';
import { executeQuery } from '../config/database.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { DashboardStats } from '../types/index.js';

const router = Router();

// Get dashboard statistics
router.get('/stats',
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const userRole = req.user!.role;
    
    let whereClause = '';
    const params: any[] = [];
    
    // Filter data based on user role
    if (userRole === 'landlord') {
      whereClause = 'WHERE p.landlord_id = ?';
      params.push(userId);
    } else if (userRole === 'property_manager') {
      whereClause = 'WHERE p.manager_id = ?';
      params.push(userId);
    }
    
    // Get property and unit statistics
    const propertyStatsQuery = `
      SELECT 
        COUNT(DISTINCT p.id) as total_properties,
        COUNT(u.id) as total_units,
        SUM(CASE WHEN u.is_occupied = TRUE THEN 1 ELSE 0 END) as occupied_units,
        SUM(CASE WHEN u.is_occupied = FALSE THEN 1 ELSE 0 END) as vacant_units,
        SUM(u.monthly_rent) as potential_revenue,
        SUM(CASE WHEN u.is_occupied = TRUE THEN u.monthly_rent ELSE 0 END) as actual_revenue
      FROM properties p
      LEFT JOIN units u ON p.id = u.property_id
      ${whereClause} AND p.is_active = TRUE
    `;
    
    const propertyStats = await executeQuery(propertyStatsQuery, params);
    const stats = propertyStats[0];
    
    // Calculate occupancy rate
    const occupancyRate = stats.total_units > 0 ? 
      Math.round((stats.occupied_units / stats.total_units) * 100) : 0;
    
    // Get invoice statistics
    let invoiceWhereClause = '';
    const invoiceParams: any[] = [];
    
    if (userRole === 'landlord') {
      invoiceWhereClause = `
        WHERE i.id IN (
          SELECT i2.id FROM invoices i2
          JOIN leases l2 ON i2.lease_id = l2.id
          JOIN units u2 ON l2.unit_id = u2.id
          JOIN properties p2 ON u2.property_id = p2.id
          WHERE p2.landlord_id = ?
        )
      `;
      invoiceParams.push(userId);
    } else if (userRole === 'property_manager') {
      invoiceWhereClause = `
        WHERE i.id IN (
          SELECT i2.id FROM invoices i2
          JOIN leases l2 ON i2.lease_id = l2.id
          JOIN units u2 ON l2.unit_id = u2.id
          JOIN properties p2 ON u2.property_id = p2.id
          WHERE p2.manager_id = ?
        )
      `;
      invoiceParams.push(userId);
    }
    
    const invoiceStatsQuery = `
      SELECT 
        COUNT(CASE WHEN status IN ('sent', 'viewed') THEN 1 END) as pending_invoices,
        COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_invoices,
        SUM(CASE WHEN status IN ('sent', 'viewed', 'overdue') THEN total_amount ELSE 0 END) as total_invoice_amount
      FROM invoices i
      ${invoiceWhereClause}
    `;
    
    const invoiceStats = await executeQuery(invoiceStatsQuery, invoiceParams);
    const invoiceData = invoiceStats[0];
    
    // Get maintenance statistics
    let maintenanceWhereClause = '';
    const maintenanceParams: any[] = [];
    
    if (userRole === 'landlord') {
      maintenanceWhereClause = `
        WHERE mr.id IN (
          SELECT mr2.id FROM maintenance_requests mr2
          JOIN units u2 ON mr2.unit_id = u2.id
          JOIN properties p2 ON u2.property_id = p2.id
          WHERE p2.landlord_id = ?
        )
      `;
      maintenanceParams.push(userId);
    } else if (userRole === 'property_manager') {
      maintenanceWhereClause = `
        WHERE mr.id IN (
          SELECT mr2.id FROM maintenance_requests mr2
          JOIN units u2 ON mr2.unit_id = u2.id
          JOIN properties p2 ON u2.property_id = p2.id
          WHERE p2.manager_id = ?
        )
      `;
      maintenanceParams.push(userId);
    } else if (userRole === 'caretaker') {
      maintenanceWhereClause = 'WHERE mr.assigned_to = ?';
      maintenanceParams.push(userId);
    }
    
    const maintenanceStatsQuery = `
      SELECT 
        COUNT(CASE WHEN status NOT IN ('completed', 'cancelled') THEN 1 END) as maintenance_requests,
        COUNT(CASE WHEN priority IN ('urgent', 'emergency') AND status NOT IN ('completed', 'cancelled') THEN 1 END) as urgent_maintenance,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_maintenance
      FROM maintenance_requests mr
      ${maintenanceWhereClause}
    `;
    
    const maintenanceStats = await executeQuery(maintenanceStatsQuery, maintenanceParams);
    const maintenanceData = maintenanceStats[0];
    
    // Get tenant statistics
    let tenantWhereClause = '';
    const tenantParams: any[] = [];
    
    if (userRole === 'landlord') {
      tenantWhereClause = `
        WHERE l.id IN (
          SELECT l2.id FROM leases l2
          JOIN units u2 ON l2.unit_id = u2.id
          JOIN properties p2 ON u2.property_id = p2.id
          WHERE p2.landlord_id = ?
        )
      `;
      tenantParams.push(userId);
    } else if (userRole === 'property_manager') {
      tenantWhereClause = `
        WHERE l.id IN (
          SELECT l2.id FROM leases l2
          JOIN units u2 ON l2.unit_id = u2.id
          JOIN properties p2 ON u2.property_id = p2.id
          WHERE p2.manager_id = ?
        )
      `;
      tenantParams.push(userId);
    }
    
    const tenantStatsQuery = `
      SELECT 
        COUNT(DISTINCT l.tenant_id) as total_tenants,
        COUNT(CASE WHEN l.start_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) THEN 1 END) as new_tenants_this_month,
        COUNT(CASE WHEN l.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 MONTH) THEN 1 END) as leases_expiring_this_month
      FROM leases l
      ${tenantWhereClause} AND l.status = 'active'
    `;
    
    const tenantStats = await executeQuery(tenantStatsQuery, tenantParams);
    const tenantData = tenantStats[0];
    
    // Compile dashboard statistics
    const dashboardStats: DashboardStats = {
      totalProperties: parseInt(stats.total_properties) || 0,
      totalUnits: parseInt(stats.total_units) || 0,
      occupiedUnits: parseInt(stats.occupied_units) || 0,
      vacantUnits: parseInt(stats.vacant_units) || 0,
      occupancyRate,
      monthlyRevenue: parseFloat(stats.potential_revenue) || 0,
      actualRevenue: parseFloat(stats.actual_revenue) || 0,
      pendingInvoices: parseInt(invoiceData.pending_invoices) || 0,
      overdueInvoices: parseInt(invoiceData.overdue_invoices) || 0,
      totalInvoiceAmount: parseFloat(invoiceData.total_invoice_amount) || 0,
      maintenanceRequests: parseInt(maintenanceData.maintenance_requests) || 0,
      urgentMaintenance: parseInt(maintenanceData.urgent_maintenance) || 0,
      completedMaintenance: parseInt(maintenanceData.completed_maintenance) || 0,
      totalTenants: parseInt(tenantData.total_tenants) || 0,
      newTenantsThisMonth: parseInt(tenantData.new_tenants_this_month) || 0,
      leasesExpiringThisMonth: parseInt(tenantData.leases_expiring_this_month) || 0
    };
    
    res.json({
      success: true,
      data: dashboardStats
    });
  })
);

// Get recent activities
router.get('/activities',
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const userRole = req.user!.role;
    const limit = parseInt(req.query.limit as string) || 10;
    
    let whereClause = '';
    const params: any[] = [];
    
    // Filter activities based on user role
    if (userRole === 'landlord') {
      whereClause = `
        WHERE (
          (al.table_name = 'properties' AND al.record_id IN (
            SELECT id FROM properties WHERE landlord_id = ?
          )) OR
          (al.table_name IN ('units', 'leases', 'invoices', 'payments', 'maintenance_requests') AND al.record_id IN (
            SELECT DISTINCT 
              CASE 
                WHEN al.table_name = 'units' THEN u.id
                WHEN al.table_name = 'leases' THEN l.id
                WHEN al.table_name = 'invoices' THEN i.id
                WHEN al.table_name = 'payments' THEN p.id
                WHEN al.table_name = 'maintenance_requests' THEN mr.id
              END
            FROM properties prop
            LEFT JOIN units u ON prop.id = u.property_id
            LEFT JOIN leases l ON u.id = l.unit_id
            LEFT JOIN invoices i ON l.id = i.lease_id
            LEFT JOIN payments p ON i.id = p.invoice_id
            LEFT JOIN maintenance_requests mr ON u.id = mr.unit_id
            WHERE prop.landlord_id = ?
          ))
        )
      `;
      params.push(userId, userId);
    } else if (userRole === 'property_manager') {
      whereClause = `
        WHERE (
          (al.table_name = 'properties' AND al.record_id IN (
            SELECT id FROM properties WHERE manager_id = ?
          )) OR
          (al.table_name IN ('units', 'leases', 'invoices', 'payments', 'maintenance_requests') AND al.record_id IN (
            SELECT DISTINCT 
              CASE 
                WHEN al.table_name = 'units' THEN u.id
                WHEN al.table_name = 'leases' THEN l.id
                WHEN al.table_name = 'invoices' THEN i.id
                WHEN al.table_name = 'payments' THEN p.id
                WHEN al.table_name = 'maintenance_requests' THEN mr.id
              END
            FROM properties prop
            LEFT JOIN units u ON prop.id = u.property_id
            LEFT JOIN leases l ON u.id = l.unit_id
            LEFT JOIN invoices i ON l.id = i.lease_id
            LEFT JOIN payments p ON i.id = p.invoice_id
            LEFT JOIN maintenance_requests mr ON u.id = mr.unit_id
            WHERE prop.manager_id = ?
          ))
        )
      `;
      params.push(userId, userId);
    } else if (userRole === 'tenant') {
      whereClause = `
        WHERE al.user_id = ? OR (
          al.table_name IN ('invoices', 'payments', 'maintenance_requests') AND al.record_id IN (
            SELECT DISTINCT 
              CASE 
                WHEN al.table_name = 'invoices' THEN i.id
                WHEN al.table_name = 'payments' THEN p.id
                WHEN al.table_name = 'maintenance_requests' THEN mr.id
              END
            FROM tenants t
            LEFT JOIN leases l ON t.id = l.tenant_id
            LEFT JOIN invoices i ON l.id = i.lease_id
            LEFT JOIN payments p ON i.id = p.invoice_id
            LEFT JOIN maintenance_requests mr ON l.unit_id = mr.unit_id
            WHERE t.user_id = ?
          )
        )
      `;
      params.push(userId, userId);
    }
    
    const activitiesQuery = `
      SELECT 
        al.action,
        al.table_name,
        al.record_id,
        al.created_at,
        CONCAT(u.first_name, ' ', u.last_name) as user_name
      FROM audit_logs al
      LEFT JOIN users u ON al.user_id = u.id
      ${whereClause}
      ORDER BY al.created_at DESC
      LIMIT ?
    `;
    
    params.push(limit);
    const activities = await executeQuery(activitiesQuery, params);
    
    res.json({
      success: true,
      data: activities
    });
  })
);

export default router;
