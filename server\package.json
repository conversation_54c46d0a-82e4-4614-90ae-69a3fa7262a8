{"name": "propertypro-backend", "version": "1.0.0", "description": "PropertyPro Enterprise Backend API", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:server": "tsx src/scripts/test-server.ts", "setup:db": "tsx src/scripts/setup-database.ts", "migrate": "tsx src/scripts/migrate.ts", "seed": "tsx src/scripts/seed.ts"}, "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.9.7", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["property-management", "rental", "real-estate", "api", "nodejs", "express"], "author": "PropertyPro Team", "license": "MIT"}