-- PropertyPro Database Setup Script
-- Run this in MySQL Workbench or command line after creating the database

USE property_management;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('landlord', 'property_manager', 'caretaker', 'tenant') NOT NULL,
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create properties table
CREATE TABLE IF NOT EXISTS properties (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Kenya',
    property_type ENUM('residential', 'commercial', 'mixed') NOT NULL,
    total_units INT DEFAULT 0,
    landlord_id VARCHAR(36) NOT NULL,
    manager_id VARCHAR(36),
    images JSON,
    amenities JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (landlord_id) REFERENCES users(id),
    FOREIGN KEY (manager_id) REFERENCES users(id)
);

-- Create units table
CREATE TABLE IF NOT EXISTS units (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    property_id VARCHAR(36) NOT NULL,
    unit_number VARCHAR(50) NOT NULL,
    unit_type ENUM('studio', '1br', '2br', '3br', '4br', 'office', 'retail', 'warehouse') NOT NULL,
    floor_number INT,
    square_feet DECIMAL(10,2),
    bedrooms INT DEFAULT 0,
    bathrooms DECIMAL(3,1) DEFAULT 0,
    monthly_rent DECIMAL(10,2) NOT NULL,
    security_deposit DECIMAL(10,2) NOT NULL,
    description TEXT,
    amenities JSON,
    images JSON,
    is_occupied BOOLEAN DEFAULT FALSE,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    UNIQUE KEY unique_unit_per_property (property_id, unit_number)
);

-- Insert sample data
-- Sample users (password is 'password123' hashed with bcrypt)
INSERT IGNORE INTO users (id, email, password_hash, first_name, last_name, phone, role, is_active, email_verified) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5ePEJ./.H.DqX9jmjKQbXD8VqKzY8QzY8QzY8Qz', 'Admin', 'User', '+254700000001', 'landlord', TRUE, TRUE),
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5ePEJ./.H.DqX9jmjKQbXD8VqKzY8QzY8QzY8Qz', 'Property', 'Manager', '+254700000002', 'property_manager', TRUE, TRUE),
('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5ePEJ./.H.DqX9jmjKQbXD8VqKzY8QzY8QzY8Qz', 'John', 'Tenant', '+254700000003', 'tenant', TRUE, TRUE);

-- Sample properties
INSERT IGNORE INTO properties (id, name, description, address, city, state, country, property_type, landlord_id, manager_id, is_active) VALUES
('660e8400-e29b-41d4-a716-446655440001', 'Sunset Apartments', 'Modern residential complex with great amenities', '123 Sunset Boulevard', 'Nairobi', 'Nairobi County', 'Kenya', 'residential', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', TRUE),
('660e8400-e29b-41d4-a716-446655440002', 'Downtown Office Complex', 'Prime commercial space in the city center', '456 Business District', 'Nairobi', 'Nairobi County', 'Kenya', 'commercial', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', TRUE),
('660e8400-e29b-41d4-a716-446655440003', 'Garden View Residences', 'Luxury apartments with garden views', '789 Garden Street', 'Mombasa', 'Mombasa County', 'Kenya', 'residential', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', TRUE);

-- Sample units for Sunset Apartments
INSERT IGNORE INTO units (id, property_id, unit_number, unit_type, bedrooms, bathrooms, monthly_rent, security_deposit, is_occupied, is_available) VALUES
('770e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'A1', '2br', 2, 2, 30000, 60000, TRUE, FALSE),
('770e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001', 'A2', '2br', 2, 2, 32000, 64000, FALSE, TRUE),
('770e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440001', 'A3', '3br', 3, 2, 45000, 90000, TRUE, FALSE),
('770e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440001', 'A4', '1br', 1, 1, 25000, 50000, FALSE, TRUE);

-- Sample units for Downtown Office Complex
INSERT IGNORE INTO units (id, property_id, unit_number, unit_type, bedrooms, bathrooms, monthly_rent, security_deposit, is_occupied, is_available) VALUES
('770e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440002', 'Office-101', 'office', 0, 1, 80000, 160000, TRUE, FALSE),
('770e8400-e29b-41d4-a716-446655440006', '660e8400-e29b-41d4-a716-446655440002', 'Office-102', 'office', 0, 1, 75000, 150000, FALSE, TRUE),
('770e8400-e29b-41d4-a716-446655440007', '660e8400-e29b-41d4-a716-446655440002', 'Office-201', 'office', 0, 2, 120000, 240000, FALSE, TRUE);

-- Sample units for Garden View Residences
INSERT IGNORE INTO units (id, property_id, unit_number, unit_type, bedrooms, bathrooms, monthly_rent, security_deposit, is_occupied, is_available) VALUES
('770e8400-e29b-41d4-a716-446655440008', '660e8400-e29b-41d4-a716-446655440003', 'B1', '2br', 2, 2, 35000, 70000, FALSE, TRUE),
('770e8400-e29b-41d4-a716-446655440009', '660e8400-e29b-41d4-a716-446655440003', 'B2', '3br', 3, 2, 50000, 100000, TRUE, FALSE),
('770e8400-e29b-41d4-a716-446655440010', '660e8400-e29b-41d4-a716-446655440003', 'B3', '2br', 2, 2, 38000, 76000, FALSE, TRUE);

-- Update property total_units and occupied_units counts
UPDATE properties p SET 
    total_units = (SELECT COUNT(*) FROM units u WHERE u.property_id = p.id);

-- Create a view for property statistics
CREATE OR REPLACE VIEW property_stats AS
SELECT 
    p.id,
    p.name,
    p.address,
    p.city,
    p.property_type,
    COUNT(u.id) as total_units,
    SUM(CASE WHEN u.is_occupied = TRUE THEN 1 ELSE 0 END) as occupied_units,
    ROUND(
        CASE 
            WHEN COUNT(u.id) > 0 THEN (SUM(CASE WHEN u.is_occupied = TRUE THEN 1 ELSE 0 END) / COUNT(u.id)) * 100 
            ELSE 0 
        END, 2
    ) as occupancy_rate,
    SUM(u.monthly_rent) as potential_revenue,
    SUM(CASE WHEN u.is_occupied = TRUE THEN u.monthly_rent ELSE 0 END) as actual_revenue,
    CONCAT(usr1.first_name, ' ', usr1.last_name) as landlord_name,
    CONCAT(usr2.first_name, ' ', usr2.last_name) as manager_name
FROM properties p
LEFT JOIN units u ON p.id = u.property_id
LEFT JOIN users usr1 ON p.landlord_id = usr1.id
LEFT JOIN users usr2 ON p.manager_id = usr2.id
WHERE p.is_active = TRUE
GROUP BY p.id, p.name, p.address, p.city, p.property_type, usr1.first_name, usr1.last_name, usr2.first_name, usr2.last_name;

-- Show setup results
SELECT 'Database setup completed!' as status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as property_count FROM properties;
SELECT COUNT(*) as unit_count FROM units;

-- Show sample login credentials
SELECT 
    'Login Credentials' as info,
    email,
    'password123' as password,
    role
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');
