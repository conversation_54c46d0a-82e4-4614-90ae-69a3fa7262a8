import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  Calendar,
  DollarSign,
  MapPin,
  User,
  Alert<PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  Download
} from 'lucide-react';
import { leasesAPI } from '../../services/api';

interface Lease {
  id: string;
  tenant_name: string;
  tenant_email: string;
  unit_number: string;
  unit_type: string;
  property_name: string;
  property_address: string;
  lease_type: string;
  start_date: string;
  end_date: string;
  monthly_rent: number;
  security_deposit: number;
  status: string;
  auto_renew: boolean;
  created_at: string;
}

const LeasesPage: React.FC = () => {
  const [leases, setLeases] = useState<Lease[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    fetchLeases();
  }, [searchTerm, filterStatus]);

  const fetchLeases = async () => {
    try {
      setLoading(true);
      const response = await leasesAPI.getAll({
        search: searchTerm,
        status: filterStatus === 'all' ? undefined : filterStatus
      });
      setLeases(response.data || []);
    } catch (error) {
      console.error('Failed to fetch leases:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'terminated': return 'bg-gray-100 text-gray-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle size={16} className="text-green-600" />;
      case 'expired': return <AlertTriangle size={16} className="text-red-600" />;
      case 'terminated': return <AlertTriangle size={16} className="text-gray-600" />;
      case 'draft': return <Clock size={16} className="text-yellow-600" />;
      default: return <Clock size={16} className="text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDaysUntilExpiry = (endDate: string) => {
    const today = new Date();
    const expiry = new Date(endDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 gap-6">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Lease Management</h1>
          <p className="text-gray-600 mt-1">Manage lease agreements, renewals, and terminations</p>
        </div>
        <button 
          onClick={() => setShowCreateModal(true)}
          className="mt-4 sm:mt-0 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus size={16} className="mr-2" />
          Create Lease
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search leases by tenant, property, or unit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter size={16} className="text-gray-400" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="draft">Draft</option>
                <option value="expired">Expired</option>
                <option value="terminated">Terminated</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Expiring Leases Alert */}
      {leases.filter(lease => lease.status === 'active' && getDaysUntilExpiry(lease.end_date) <= 30 && getDaysUntilExpiry(lease.end_date) > 0).length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle size={20} className="text-yellow-600 mr-3" />
            <div>
              <p className="font-medium text-yellow-800">
                {leases.filter(lease => lease.status === 'active' && getDaysUntilExpiry(lease.end_date) <= 30 && getDaysUntilExpiry(lease.end_date) > 0).length} lease(s) expiring within 30 days
              </p>
              <p className="text-sm text-yellow-600">Review and prepare renewal agreements</p>
            </div>
          </div>
        </div>
      )}

      {/* Leases List */}
      <div className="bg-white rounded-xl shadow-sm border">
        {leases.length === 0 ? (
          <div className="text-center py-12">
            <FileText size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No leases found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || filterStatus !== 'all' 
                ? 'Try adjusting your search or filter criteria.' 
                : 'Get started by creating your first lease agreement.'}
            </p>
            <button 
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} className="inline mr-2" />
              Create Lease
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-6 font-medium text-gray-600">Tenant & Property</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600">Lease Period</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600">Financial</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600">Status</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600">Expiry</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {leases.map((lease) => {
                  const daysUntilExpiry = getDaysUntilExpiry(lease.end_date);
                  return (
                    <tr key={lease.id} className="hover:bg-gray-50">
                      <td className="py-4 px-6">
                        <div>
                          <div className="flex items-center mb-2">
                            <User size={16} className="text-gray-400 mr-2" />
                            <span className="font-medium text-gray-900">{lease.tenant_name}</span>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <MapPin size={14} className="mr-2" />
                            {lease.property_name} - Unit {lease.unit_number}
                          </div>
                          <p className="text-xs text-gray-500 mt-1">{lease.unit_type}</p>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="space-y-1">
                          <div className="flex items-center text-sm">
                            <Calendar size={14} className="mr-2 text-gray-400" />
                            <span className="text-gray-900">{formatDate(lease.start_date)}</span>
                          </div>
                          <div className="flex items-center text-sm">
                            <span className="ml-6 text-gray-600">to {formatDate(lease.end_date)}</span>
                          </div>
                          <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                            {lease.lease_type}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="space-y-1">
                          <div className="flex items-center text-sm">
                            <DollarSign size={14} className="mr-1 text-green-600" />
                            <span className="font-medium text-gray-900">
                              KES {lease.monthly_rent.toLocaleString()}/month
                            </span>
                          </div>
                          <p className="text-xs text-gray-600">
                            Deposit: KES {lease.security_deposit.toLocaleString()}
                          </p>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center">
                          {getStatusIcon(lease.status)}
                          <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(lease.status)}`}>
                            {lease.status}
                          </span>
                        </div>
                        {lease.auto_renew && (
                          <p className="text-xs text-blue-600 mt-1">Auto-renew enabled</p>
                        )}
                      </td>
                      <td className="py-4 px-6">
                        {lease.status === 'active' && (
                          <div>
                            <p className={`text-sm font-medium ${
                              daysUntilExpiry <= 30 ? 'text-red-600' : 
                              daysUntilExpiry <= 60 ? 'text-yellow-600' : 'text-green-600'
                            }`}>
                              {daysUntilExpiry > 0 ? `${daysUntilExpiry} days` : 'Expired'}
                            </p>
                            <p className="text-xs text-gray-500">
                              {daysUntilExpiry <= 30 && daysUntilExpiry > 0 ? 'Expiring soon' : 
                               daysUntilExpiry <= 0 ? 'Expired' : 'Active'}
                            </p>
                          </div>
                        )}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <Eye size={16} />
                          </button>
                          <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                            <Edit size={16} />
                          </button>
                          <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                            <Download size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Leases</p>
              <p className="text-2xl font-bold text-gray-900">{leases.length}</p>
            </div>
            <FileText size={24} className="text-blue-600" />
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Leases</p>
              <p className="text-2xl font-bold text-gray-900">
                {leases.filter(l => l.status === 'active').length}
              </p>
            </div>
            <CheckCircle size={24} className="text-green-600" />
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
              <p className="text-2xl font-bold text-gray-900">
                {leases.filter(l => l.status === 'active' && getDaysUntilExpiry(l.end_date) <= 30 && getDaysUntilExpiry(l.end_date) > 0).length}
              </p>
            </div>
            <AlertTriangle size={24} className="text-yellow-600" />
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                KES {leases.filter(l => l.status === 'active')
                  .reduce((sum, l) => sum + l.monthly_rent, 0)
                  .toLocaleString()}
              </p>
            </div>
            <DollarSign size={24} className="text-green-600" />
          </div>
        </div>
      </div>

      {/* Create Lease Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Lease</h3>
            <p className="text-gray-600 mb-4">
              Create lease functionality will be implemented here with form fields for lease details.
            </p>
            <div className="flex space-x-3">
              <button 
                onClick={() => setShowCreateModal(false)}
                className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                Create Lease
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeasesPage;
