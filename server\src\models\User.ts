import { executeQuery, executeTransaction } from '../config/database.js';
import { User, CreateUserRequest } from '../types/index.js';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

export class UserModel {
  // Create a new user
  static async create(userData: CreateUserRequest): Promise<User> {
    const id = uuidv4();
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    
    const query = `
      INSERT INTO users (
        id, email, password_hash, first_name, last_name, phone, role, is_active, email_verified
      ) VALUES (?, ?, ?, ?, ?, ?, ?, TRUE, FALSE)
    `;
    
    const params = [
      id,
      userData.email,
      hashedPassword,
      userData.first_name,
      userData.last_name,
      userData.phone || null,
      userData.role
    ];
    
    await executeQuery(query, params);
    
    // Return the created user (without password)
    return await this.findById(id) as User;
  }

  // Find user by ID
  static async findById(id: string): Promise<User | null> {
    const query = `
      SELECT id, email, first_name, last_name, phone, role, avatar_url, 
             is_active, email_verified, last_login, created_at, updated_at
      FROM users 
      WHERE id = ? AND is_active = TRUE
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  // Find user by email
  static async findByEmail(email: string): Promise<User | null> {
    const query = `
      SELECT id, email, password_hash, first_name, last_name, phone, role, 
             avatar_url, is_active, email_verified, last_login, created_at, updated_at
      FROM users 
      WHERE email = ? AND is_active = TRUE
    `;
    
    const results = await executeQuery(query, [email]);
    return results.length > 0 ? results[0] : null;
  }

  // Update user
  static async update(id: string, updateData: Partial<User>): Promise<User | null> {
    const allowedFields = ['first_name', 'last_name', 'phone', 'avatar_url', 'email_verified'];
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key) && updateData[key as keyof User] !== undefined) {
        updates.push(`${key} = ?`);
        params.push(updateData[key as keyof User]);
      }
    });
    
    if (updates.length === 0) {
      return await this.findById(id);
    }
    
    params.push(id);
    
    const query = `
      UPDATE users 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_active = TRUE
    `;
    
    await executeQuery(query, params);
    return await this.findById(id);
  }

  // Update password
  static async updatePassword(id: string, newPassword: string): Promise<boolean> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    const query = `
      UPDATE users 
      SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_active = TRUE
    `;
    
    const result = await executeQuery(query, [hashedPassword, id]);
    return result.affectedRows > 0;
  }

  // Update last login
  static async updateLastLogin(id: string): Promise<void> {
    const query = `
      UPDATE users 
      SET last_login = CURRENT_TIMESTAMP
      WHERE id = ? AND is_active = TRUE
    `;
    
    await executeQuery(query, [id]);
  }

  // Verify password
  static async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // Get all users with pagination
  static async findAll(
    page: number = 1, 
    limit: number = 10, 
    role?: string,
    search?: string
  ): Promise<{ users: User[]; total: number }> {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE is_active = TRUE';
    const params: any[] = [];
    
    if (role) {
      whereClause += ' AND role = ?';
      params.push(role);
    }
    
    if (search) {
      whereClause += ' AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;
    
    // Get users
    const query = `
      SELECT id, email, first_name, last_name, phone, role, avatar_url, 
             is_active, email_verified, last_login, created_at, updated_at
      FROM users 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const users = await executeQuery(query, params);
    
    return { users, total };
  }

  // Soft delete user
  static async delete(id: string): Promise<boolean> {
    const query = `
      UPDATE users 
      SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // Check if email exists
  static async emailExists(email: string, excludeId?: string): Promise<boolean> {
    let query = 'SELECT COUNT(*) as count FROM users WHERE email = ? AND is_active = TRUE';
    const params: any[] = [email];
    
    if (excludeId) {
      query += ' AND id != ?';
      params.push(excludeId);
    }
    
    const result = await executeQuery(query, params);
    return result[0].count > 0;
  }

  // Get users by role
  static async findByRole(role: string): Promise<User[]> {
    const query = `
      SELECT id, email, first_name, last_name, phone, role, avatar_url, 
             is_active, email_verified, last_login, created_at, updated_at
      FROM users 
      WHERE role = ? AND is_active = TRUE
      ORDER BY first_name, last_name
    `;
    
    return await executeQuery(query, [role]);
  }

  // Get user statistics
  static async getStats(): Promise<any> {
    const query = `
      SELECT 
        role,
        COUNT(*) as count,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as active_last_30_days
      FROM users 
      WHERE is_active = TRUE
      GROUP BY role
    `;
    
    return await executeQuery(query);
  }
}
