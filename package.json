{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "setup": "node scripts/setup.js", "test:db": "node test-database.js", "backend": "cd server && npm run dev", "start:dev": "concurrently \"npm run dev\" \"npm run backend\""}, "dependencies": {"@types/react-router-dom": "^5.3.3", "date-fns": "^4.1.0", "lucide-react": "^0.344.0", "mysql2": "^3.14.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-router-dom": "^7.6.3", "recharts": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}