import React from 'react';
import { 
  Building, 
  Users, 
  DollarSign, 
  Wrench, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

const DashboardOverview = () => {
  const { user } = useAuth();

  const stats = [
    {
      title: 'Total Properties',
      value: '8',
      change: '+2',
      changeType: 'positive',
      icon: Building,
      color: 'bg-blue-500'
    },
    {
      title: 'Occupied Units',
      value: '45/50',
      change: '90%',
      changeType: 'positive',
      icon: Users,
      color: 'bg-green-500'
    },
    {
      title: 'Monthly Revenue',
      value: '$125,400',
      change: '+8.2%',
      changeType: 'positive',
      icon: DollarSign,
      color: 'bg-emerald-500'
    },
    {
      title: 'Maintenance Requests',
      value: '12',
      change: '3 urgent',
      changeType: 'negative',
      icon: Wrench,
      color: 'bg-orange-500'
    }
  ];

  const recentActivities = [
    { id: 1, action: 'Payment received', description: 'Emily Davis - Unit A101 - $2,500', time: '2 hours ago', type: 'payment' },
    { id: 2, action: 'Maintenance request', description: 'Unit A102 - AC repair needed', time: '4 hours ago', type: 'maintenance' },
    { id: 3, action: 'New lease signed', description: 'Michael Brown - Unit B203', time: '1 day ago', type: 'lease' },
    { id: 4, action: 'Invoice generated', description: 'December rent - 15 units', time: '2 days ago', type: 'invoice' }
  ];

  const urgentItems = [
    { id: 1, title: 'Lease Expiring Soon', description: '3 leases expire this month', priority: 'high' },
    { id: 2, title: 'Overdue Payments', description: '$8,750 in overdue rent', priority: 'urgent' },
    { id: 3, title: 'Urgent Maintenance', description: '2 urgent repair requests', priority: 'urgent' },
    { id: 4, title: 'Vacant Units', description: '5 units available for rent', priority: 'medium' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}
        </h1>
        <div className="text-sm text-gray-500">
          {new Date().toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.title} className="bg-white rounded-xl shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">{stat.value}</p>
                <div className="flex items-center mt-2">
                  {stat.changeType === 'positive' ? (
                    <TrendingUp size={16} className="text-green-500 mr-1" />
                  ) : (
                    <TrendingDown size={16} className="text-red-500 mr-1" />
                  )}
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </span>
                </div>
              </div>
              <div className={`p-3 rounded-full ${stat.color}`}>
                <stat.icon size={24} className="text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <div className="bg-white rounded-xl shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Activities</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full ${
                    activity.type === 'payment' ? 'bg-green-100' :
                    activity.type === 'maintenance' ? 'bg-orange-100' :
                    activity.type === 'lease' ? 'bg-blue-100' : 'bg-purple-100'
                  }`}>
                    {activity.type === 'payment' && <DollarSign size={16} className="text-green-600" />}
                    {activity.type === 'maintenance' && <Wrench size={16} className="text-orange-600" />}
                    {activity.type === 'lease' && <Users size={16} className="text-blue-600" />}
                    {activity.type === 'invoice' && <CheckCircle size={16} className="text-purple-600" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-sm text-gray-500">{activity.description}</p>
                    <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Urgent Items */}
        <div className="bg-white rounded-xl shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Urgent Items</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {urgentItems.map((item) => (
                <div key={item.id} className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full ${
                    item.priority === 'urgent' ? 'bg-red-100' :
                    item.priority === 'high' ? 'bg-orange-100' : 'bg-yellow-100'
                  }`}>
                    <AlertTriangle size={16} className={
                      item.priority === 'urgent' ? 'text-red-600' :
                      item.priority === 'high' ? 'text-orange-600' : 'text-yellow-600'
                    } />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{item.title}</p>
                    <p className="text-sm text-gray-500">{item.description}</p>
                    <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full mt-2 ${
                      item.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                      item.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;