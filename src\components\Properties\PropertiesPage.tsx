import React, { useState } from 'react';
import { Plus, Building, MapPin, Users, DollarSign, Search, Filter } from 'lucide-react';
import { mockProperties, mockUnits } from '../../data/mockData';

const PropertiesPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  const getPropertyStats = (propertyId: string) => {
    const units = mockUnits.filter(unit => unit.propertyId === propertyId);
    const occupiedUnits = units.filter(unit => unit.isOccupied);
    const totalRent = units.reduce((sum, unit) => sum + unit.rent, 0);
    
    return {
      totalUnits: units.length,
      occupiedUnits: occupiedUnits.length,
      occupancyRate: units.length > 0 ? (occupiedUnits.length / units.length) * 100 : 0,
      monthlyRevenue: occupiedUnits.reduce((sum, unit) => sum + unit.rent, 0),
      potentialRevenue: totalRent
    };
  };

  const filteredProperties = mockProperties.filter(property => {
    const matchesSearch = property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.address.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || property.type === filterType;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Properties</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 text-sm sm:text-base">
          <Plus size={18} />
          <span>Add Property</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
        <div className="relative flex-1">
          <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search properties..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 sm:py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
          />
        </div>
        <div className="flex items-center space-x-2 sm:space-x-2">
          <Filter size={18} className="text-gray-400 flex-shrink-0" />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="flex-1 sm:flex-none border border-gray-300 rounded-lg px-3 py-2.5 sm:py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
          >
            <option value="all">All Types</option>
            <option value="residential">Residential</option>
            <option value="commercial">Commercial</option>
          </select>
        </div>
      </div>

      {/* Properties Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {filteredProperties.map((property) => {
          const stats = getPropertyStats(property.id);
          return (
            <div key={property.id} className="bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
              <div className="p-4 sm:p-6">
                <div className="flex flex-wrap items-start justify-between mb-3 sm:mb-4 gap-2">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">{property.name}</h3>
                    <div className="flex items-center text-gray-500 mt-1">
                      <MapPin size={14} className="mr-1 flex-shrink-0" />
                      <span className="text-xs sm:text-sm truncate">{property.address}</span>
                    </div>
                  </div>
                  <span className={`px-2 sm:px-3 py-1 text-xs font-medium rounded-full flex-shrink-0 ${
                    property.type === 'residential'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-purple-100 text-purple-800'
                  }`}>
                    {property.type.charAt(0).toUpperCase() + property.type.slice(1)}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
                  <div className="bg-gray-50 rounded-lg p-2.5 sm:p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-xs sm:text-sm font-medium text-gray-600">Units</span>
                      <Building size={14} className="text-gray-400 sm:w-4 sm:h-4" />
                    </div>
                    <div className="mt-1">
                      <span className="text-base sm:text-lg font-bold text-gray-900">{stats.totalUnits}</span>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-2.5 sm:p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-xs sm:text-sm font-medium text-gray-600">Occupied</span>
                      <Users size={14} className="text-gray-400 sm:w-4 sm:h-4" />
                    </div>
                    <div className="mt-1">
                      <span className="text-base sm:text-lg font-bold text-gray-900">{stats.occupiedUnits}</span>
                      <span className="text-xs sm:text-sm text-gray-500 ml-1">
                        ({stats.occupancyRate.toFixed(0)}%)
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-2.5 sm:p-3 mb-3 sm:mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-xs sm:text-sm font-medium text-green-700">Monthly Revenue</span>
                    <DollarSign size={14} className="text-green-600 sm:w-4 sm:h-4" />
                  </div>
                  <div className="mt-1">
                    <span className="text-base sm:text-lg font-bold text-green-900">
                      ${stats.monthlyRevenue.toLocaleString()}
                    </span>
                    <span className="text-xs sm:text-sm text-green-600 ml-1">
                      / ${stats.potentialRevenue.toLocaleString()}
                    </span>
                  </div>
                </div>

                <div className="flex space-x-2 sm:space-x-3">
                  <button className="flex-1 bg-blue-600 text-white py-2 px-3 sm:px-4 rounded-lg hover:bg-blue-700 transition-colors text-xs sm:text-sm font-medium">
                    View Details
                  </button>
                  <button className="px-3 sm:px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-xs sm:text-sm font-medium">
                    Edit
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredProperties.length === 0 && (
        <div className="text-center py-12">
          <Building size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
          <p className="text-gray-500">Try adjusting your search or add a new property.</p>
        </div>
      )}
    </div>
  );
};

export default PropertiesPage;