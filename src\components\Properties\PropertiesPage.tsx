import React, { useState, useEffect } from 'react';
import { Plus, Building, MapPin, Users, DollarSign, Search, Filter } from 'lucide-react';
import { propertiesAPI } from '../../services/api';

interface Property {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state?: string;
  country: string;
  property_type: 'residential' | 'commercial' | 'mixed';
  total_units: number;
  occupied_units: number;
  landlord_name?: string;
  manager_name?: string;
  created_at: string;
  updated_at: string;
}

const PropertiesPage = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProperties();
  }, []);

  const fetchProperties = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await propertiesAPI.getAll({
        search: searchTerm,
        propertyType: filterType === 'all' ? undefined : filterType
      });
      setProperties(response.data || []);
    } catch (error) {
      console.error('Failed to fetch properties:', error);
      setError('Failed to load properties. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Refetch when search or filter changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchProperties();
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filterType]);

  const getPropertyStats = (property: Property) => {
    const totalUnits = property.total_units || 0;
    const occupiedUnits = property.occupied_units || 0;
    const occupancyRate = totalUnits > 0 ? (occupiedUnits / totalUnits) * 100 : 0;

    // Estimate revenue based on average rent (this would come from units API in real implementation)
    const estimatedRentPerUnit = property.property_type === 'commercial' ? 50000 : 25000;
    const potentialRevenue = totalUnits * estimatedRentPerUnit;
    const monthlyRevenue = occupiedUnits * estimatedRentPerUnit;

    return {
      totalUnits,
      occupiedUnits,
      occupancyRate,
      monthlyRevenue,
      potentialRevenue
    };
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Properties</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 text-sm sm:text-base">
          <Plus size={18} />
          <span>Add Property</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
        <div className="relative flex-1">
          <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search properties..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 sm:py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
          />
        </div>
        <div className="flex items-center space-x-2 sm:space-x-2">
          <Filter size={18} className="text-gray-400 flex-shrink-0" />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="flex-1 sm:flex-none border border-gray-300 rounded-lg px-3 py-2.5 sm:py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
          >
            <option value="all">All Types</option>
            <option value="residential">Residential</option>
            <option value="commercial">Commercial</option>
          </select>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading properties...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
          <button
            onClick={fetchProperties}
            className="mt-2 text-red-700 hover:text-red-900 font-medium"
          >
            Try again
          </button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && properties.length === 0 && (
        <div className="text-center py-12">
          <Building size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || filterType !== 'all'
              ? 'Try adjusting your search or filter criteria.'
              : 'Get started by adding your first property.'}
          </p>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <Plus size={16} className="inline mr-2" />
            Add Property
          </button>
        </div>
      )}

      {/* Properties Grid */}
      {!loading && !error && properties.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {properties.map((property) => {
            const stats = getPropertyStats(property);
            return (
              <div key={property.id} className="bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
                <div className="p-4 sm:p-6">
                  <div className="flex flex-wrap items-start justify-between mb-3 sm:mb-4 gap-2">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">{property.name}</h3>
                      <div className="flex items-center text-gray-500 mt-1">
                        <MapPin size={14} className="mr-1 flex-shrink-0" />
                        <span className="text-xs sm:text-sm truncate">{property.address}, {property.city}</span>
                      </div>
                      {property.description && (
                        <p className="text-xs sm:text-sm text-gray-600 mt-1 line-clamp-2">{property.description}</p>
                      )}
                    </div>
                    <span className={`px-2 sm:px-3 py-1 text-xs font-medium rounded-full flex-shrink-0 ${
                      property.property_type === 'residential'
                        ? 'bg-blue-100 text-blue-800'
                        : property.property_type === 'commercial'
                        ? 'bg-purple-100 text-purple-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {property.property_type.charAt(0).toUpperCase() + property.property_type.slice(1)}
                    </span>
                  </div>

                <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
                  <div className="bg-gray-50 rounded-lg p-2.5 sm:p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-xs sm:text-sm font-medium text-gray-600">Units</span>
                      <Building size={14} className="text-gray-400 sm:w-4 sm:h-4" />
                    </div>
                    <div className="mt-1">
                      <span className="text-base sm:text-lg font-bold text-gray-900">{stats.totalUnits}</span>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-2.5 sm:p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-xs sm:text-sm font-medium text-gray-600">Occupied</span>
                      <Users size={14} className="text-gray-400 sm:w-4 sm:h-4" />
                    </div>
                    <div className="mt-1">
                      <span className="text-base sm:text-lg font-bold text-gray-900">{stats.occupiedUnits}</span>
                      <span className="text-xs sm:text-sm text-gray-500 ml-1">
                        ({stats.occupancyRate.toFixed(0)}%)
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-2.5 sm:p-3 mb-3 sm:mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-xs sm:text-sm font-medium text-green-700">Monthly Revenue</span>
                    <DollarSign size={14} className="text-green-600 sm:w-4 sm:h-4" />
                  </div>
                  <div className="mt-1">
                    <span className="text-base sm:text-lg font-bold text-green-900">
                      KES {stats.monthlyRevenue.toLocaleString()}
                    </span>
                    <span className="text-xs sm:text-sm text-green-600 ml-1">
                      / KES {stats.potentialRevenue.toLocaleString()}
                    </span>
                  </div>
                </div>

                <div className="flex space-x-2 sm:space-x-3">
                  <button className="flex-1 bg-blue-600 text-white py-2 px-3 sm:px-4 rounded-lg hover:bg-blue-700 transition-colors text-xs sm:text-sm font-medium">
                    View Details
                  </button>
                  <button className="px-3 sm:px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-xs sm:text-sm font-medium">
                    Edit
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      )}
    </div>
  );
};

export default PropertiesPage;