import React, { useState, useEffect } from 'react';
import { 
  Building, 
  Users, 
  DollarSign, 
  Wrench, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Calendar,
  FileText,
  CreditCard,
  Clock
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { DashboardStats } from '../../types';

const EnhancedDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch dashboard stats
    const fetchStats = async () => {
      setLoading(true);
      // This would be replaced with actual API calls
      const mockStats: DashboardStats = {
        totalProperties: 8,
        totalUnits: 50,
        occupiedUnits: 45,
        occupancyRate: 90,
        monthlyRevenue: 125400,
        pendingInvoices: 12,
        overdueInvoices: 3,
        maintenanceRequests: 8,
        urgentMaintenance: 2
      };
      
      setTimeout(() => {
        setStats(mockStats);
        setLoading(false);
      }, 1000);
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: 'Total Properties',
      value: stats?.totalProperties.toString() || '0',
      change: '+2 this month',
      changeType: 'positive' as const,
      icon: Building,
      color: 'bg-blue-500',
      href: '/properties'
    },
    {
      title: 'Occupancy Rate',
      value: `${stats?.occupancyRate || 0}%`,
      change: `${stats?.occupiedUnits || 0}/${stats?.totalUnits || 0} units`,
      changeType: 'positive' as const,
      icon: Users,
      color: 'bg-green-500',
      href: '/properties'
    },
    {
      title: 'Monthly Revenue',
      value: `KES ${(stats?.monthlyRevenue || 0).toLocaleString()}`,
      change: '+8.2% from last month',
      changeType: 'positive' as const,
      icon: DollarSign,
      color: 'bg-emerald-500',
      href: '/invoices'
    },
    {
      title: 'Maintenance Requests',
      value: stats?.maintenanceRequests.toString() || '0',
      change: `${stats?.urgentMaintenance || 0} urgent`,
      changeType: stats && stats.urgentMaintenance > 0 ? 'negative' as const : 'neutral' as const,
      icon: Wrench,
      color: 'bg-orange-500',
      href: '/maintenance'
    }
  ];

  const quickActions = [
    {
      title: 'Generate Invoices',
      description: 'Create monthly rent invoices',
      icon: FileText,
      color: 'bg-blue-600',
      action: () => console.log('Generate invoices')
    },
    {
      title: 'Add Property',
      description: 'Register a new property',
      icon: Building,
      color: 'bg-green-600',
      action: () => console.log('Add property')
    },
    {
      title: 'View Payments',
      description: 'Check recent payments',
      icon: CreditCard,
      color: 'bg-purple-600',
      action: () => console.log('View payments')
    },
    {
      title: 'Maintenance Report',
      description: 'Generate maintenance report',
      icon: Wrench,
      color: 'bg-orange-600',
      action: () => console.log('Maintenance report')
    }
  ];

  const recentActivities = [
    { 
      id: 1, 
      action: 'Payment received', 
      description: 'Emily Davis - Unit A101 - KES 2,500', 
      time: '2 hours ago', 
      type: 'payment',
      icon: DollarSign,
      color: 'text-green-600'
    },
    { 
      id: 2, 
      action: 'Maintenance request', 
      description: 'Unit A102 - AC repair needed', 
      time: '4 hours ago', 
      type: 'maintenance',
      icon: Wrench,
      color: 'text-orange-600'
    },
    { 
      id: 3, 
      action: 'New lease signed', 
      description: 'Michael Brown - Unit B203', 
      time: '1 day ago', 
      type: 'lease',
      icon: FileText,
      color: 'text-blue-600'
    },
    { 
      id: 4, 
      action: 'Invoice generated', 
      description: 'December rent - 15 units', 
      time: '2 days ago', 
      type: 'invoice',
      icon: CheckCircle,
      color: 'text-purple-600'
    }
  ];

  const urgentItems = [
    { 
      id: 1, 
      title: 'Lease Expiring Soon', 
      description: '3 leases expire this month', 
      priority: 'high' as const,
      dueDate: '2024-12-31',
      action: 'Review Leases'
    },
    { 
      id: 2, 
      title: 'Overdue Payments', 
      description: 'KES 8,750 in overdue rent', 
      priority: 'urgent' as const,
      dueDate: '2024-11-15',
      action: 'Send Reminders'
    },
    { 
      id: 3, 
      title: 'Urgent Maintenance', 
      description: '2 urgent repair requests', 
      priority: 'urgent' as const,
      dueDate: 'Today',
      action: 'Assign Technician'
    },
    { 
      id: 4, 
      title: 'Vacant Units', 
      description: '5 units available for rent', 
      priority: 'medium' as const,
      dueDate: 'Ongoing',
      action: 'Market Units'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.name}
          </h1>
          <p className="text-gray-600 mt-1">
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
            <Calendar size={20} />
            <span>Schedule</span>
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
            <FileText size={20} />
            <span>Generate Report</span>
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => (
          <div key={stat.title} className="bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow cursor-pointer">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-2">{stat.value}</p>
                  <div className="flex items-center mt-2">
                    {stat.changeType === 'positive' ? (
                      <TrendingUp size={16} className="text-green-500 mr-1" />
                    ) : stat.changeType === 'negative' ? (
                      <TrendingDown size={16} className="text-red-500 mr-1" />
                    ) : (
                      <Clock size={16} className="text-gray-500 mr-1" />
                    )}
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'positive' ? 'text-green-600' : 
                      stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-full ${stat.color}`}>
                  <stat.icon size={24} className="text-white" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <button
                key={action.title}
                onClick={action.action}
                className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all text-left group"
              >
                <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg ${action.color} mb-3 group-hover:scale-110 transition-transform`}>
                  <action.icon size={20} className="text-white" />
                </div>
                <h3 className="font-medium text-gray-900 mb-1">{action.title}</h3>
                <p className="text-sm text-gray-500">{action.description}</p>
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <div className="bg-white rounded-xl shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Activities</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full bg-gray-100`}>
                    <activity.icon size={16} className={activity.color} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-sm text-gray-500">{activity.description}</p>
                    <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                View all activities →
              </button>
            </div>
          </div>
        </div>

        {/* Urgent Items */}
        <div className="bg-white rounded-xl shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Urgent Items</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {urgentItems.map((item) => (
                <div key={item.id} className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-full ${
                      item.priority === 'urgent' ? 'bg-red-100' :
                      item.priority === 'high' ? 'bg-orange-100' : 'bg-yellow-100'
                    }`}>
                      <AlertTriangle size={16} className={
                        item.priority === 'urgent' ? 'text-red-600' :
                        item.priority === 'high' ? 'text-orange-600' : 'text-yellow-600'
                      } />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{item.title}</p>
                      <p className="text-sm text-gray-500">{item.description}</p>
                      <p className="text-xs text-gray-400 mt-1">Due: {item.dueDate}</p>
                    </div>
                  </div>
                  <button className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full transition-colors">
                    {item.action}
                  </button>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                View all urgent items →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedDashboard;