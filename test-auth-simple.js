// Simple test to verify authentication is working
const fetch = require('node-fetch');

const API_URL = 'http://localhost:5000/api/v1';

async function testAuthentication() {
  console.log('🔐 Testing Authentication Flow...\n');

  try {
    // Test 1: Login with landlord credentials
    console.log('1. Testing login...');
    const loginResponse = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginData.message);
      return;
    }

    console.log('✅ Login successful');
    console.log('   User:', loginData.data.user.email);
    console.log('   Role:', loginData.data.user.role);
    console.log('   Token received:', !!loginData.data.token);

    const token = loginData.data.token;

    // Test 2: Test authentication debug endpoint
    console.log('\n2. Testing authentication debug endpoint...');
    const debugResponse = await fetch(`${API_URL}/auth/debug`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const debugData = await debugResponse.json();
    
    if (!debugResponse.ok) {
      console.log('❌ Debug endpoint failed:', debugData.message);
      return;
    }

    console.log('✅ Authentication debug successful');
    console.log('   User role:', debugData.data.user.role);
    console.log('   Can create properties:', debugData.data.permissions.canCreateProperties);
    console.log('   Can create tenants:', debugData.data.permissions.canCreateTenants);
    console.log('   Can create invoices:', debugData.data.permissions.canCreateInvoices);

    // Test 3: Test properties endpoint
    console.log('\n3. Testing properties endpoint...');
    const propertiesResponse = await fetch(`${API_URL}/properties`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const propertiesData = await propertiesResponse.json();
    
    if (!propertiesResponse.ok) {
      console.log('❌ Properties endpoint failed:', propertiesData.message);
      console.log('   Status:', propertiesResponse.status);
      console.log('   Error:', propertiesData.error);
      return;
    }

    console.log('✅ Properties endpoint successful');
    console.log('   Properties found:', propertiesData.data?.properties?.length || 0);

    // Test 4: Test creating a property
    console.log('\n4. Testing property creation...');
    const createPropertyResponse = await fetch(`${API_URL}/properties`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'Test Property Auth',
        description: 'A test property to verify authentication',
        address: '123 Auth Test Street',
        city: 'Nairobi',
        state: 'Nairobi County',
        country: 'Kenya',
        property_type: 'residential'
      })
    });

    const createPropertyData = await createPropertyResponse.json();
    
    if (!createPropertyResponse.ok) {
      console.log('❌ Property creation failed:', createPropertyData.message);
      console.log('   Status:', createPropertyResponse.status);
      console.log('   Error:', createPropertyData.error);
      return;
    }

    console.log('✅ Property creation successful');
    console.log('   Property ID:', createPropertyData.data.id);
    console.log('   Property name:', createPropertyData.data.name);

    // Test 5: Test tenants endpoint
    console.log('\n5. Testing tenants endpoint...');
    const tenantsResponse = await fetch(`${API_URL}/tenants`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const tenantsData = await tenantsResponse.json();
    
    if (!tenantsResponse.ok) {
      console.log('❌ Tenants endpoint failed:', tenantsData.message);
      console.log('   Status:', tenantsResponse.status);
      console.log('   Error:', tenantsData.error);
      return;
    }

    console.log('✅ Tenants endpoint successful');
    console.log('   Tenants found:', tenantsData.data?.tenants?.length || 0);

    // Test 6: Test invoices endpoint
    console.log('\n6. Testing invoices endpoint...');
    const invoicesResponse = await fetch(`${API_URL}/invoices`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const invoicesData = await invoicesResponse.json();
    
    if (!invoicesResponse.ok) {
      console.log('❌ Invoices endpoint failed:', invoicesData.message);
      console.log('   Status:', invoicesResponse.status);
      console.log('   Error:', invoicesData.error);
      return;
    }

    console.log('✅ Invoices endpoint successful');
    console.log('   Invoices found:', invoicesData.data?.invoices?.length || 0);

    console.log('\n🎉 All authentication tests passed!');
    console.log('\n📋 Summary:');
    console.log('✅ Login working');
    console.log('✅ JWT token authentication working');
    console.log('✅ Role-based authorization working');
    console.log('✅ Properties CRUD accessible');
    console.log('✅ Tenants CRUD accessible');
    console.log('✅ Invoices CRUD accessible');

  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
  }
}

// Run the test
testAuthentication();
