# PropertyPro Backend Testing Guide

## ✅ What We've Accomplished

### 🔧 Backend Setup Complete
- ✅ Express.js server with TypeScript
- ✅ Database connection configuration
- ✅ JWT authentication system
- ✅ API endpoints for auth, properties, units, dashboard
- ✅ Mobile-responsive frontend updates
- ✅ Environment configuration with secure JWT secrets

### 🔐 Security Configuration
- ✅ JWT secrets properly configured in `server/.env`
- ✅ Database connection tested and working
- ✅ Role-based access control implemented

## 🚀 Manual Testing Instructions

### Step 1: Database Setup
Since the automated migration had issues, please set up the database manually:

1. **Open MySQL Workbench or command line**
2. **Create the database:**
   ```sql
   CREATE DATABASE IF NOT EXISTS property_management;
   USE property_management;
   ```

3. **Import the schema:**
   - Open the file: `supabase/migrations/20250710060353_old_dream.sql`
   - Copy the entire content
   - Execute it in MySQL Workbench or command line

### Step 2: Start the Backend Server

1. **Open a new terminal/command prompt**
2. **Navigate to the server directory:**
   ```bash
   cd server
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

   You should see output like:
   ```
   🚀 PropertyPro Backend Server running on port 5000
   📊 Environment: development
   🔗 API Base URL: http://localhost:5000/api/v1
   💚 Health Check: http://localhost:5000/health
   ```

### Step 3: Test the API Endpoints

#### Option A: Using the Test Script
1. **In a new terminal, from the project root:**
   ```bash
   node test-api.js
   ```

#### Option B: Manual Testing with Browser/Postman

1. **Health Check:**
   - Open: http://localhost:5000/health
   - Should return: `{"status":"OK","timestamp":"...","uptime":...}`

2. **Register a User:**
   ```bash
   POST http://localhost:5000/api/v1/auth/register
   Content-Type: application/json
   
   {
     "email": "<EMAIL>",
     "password": "password123",
     "first_name": "Admin",
     "last_name": "User",
     "role": "landlord"
   }
   ```

3. **Login:**
   ```bash
   POST http://localhost:5000/api/v1/auth/login
   Content-Type: application/json
   
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

4. **Test Protected Endpoint (use token from login):**
   ```bash
   GET http://localhost:5000/api/v1/auth/me
   Authorization: Bearer YOUR_JWT_TOKEN_HERE
   ```

5. **Test Dashboard Stats:**
   ```bash
   GET http://localhost:5000/api/v1/dashboard/stats
   Authorization: Bearer YOUR_JWT_TOKEN_HERE
   ```

### Step 4: Start the Frontend

1. **In another terminal, from the project root:**
   ```bash
   npm run dev
   ```

2. **Open your browser to:**
   ```
   http://localhost:5173
   ```

3. **Test the login with:**
   - Email: <EMAIL>
   - Password: password123

## 🔧 Troubleshooting

### Database Connection Issues
- Ensure MySQL server is running
- Check credentials in `server/.env`
- Verify database `property_management` exists

### Server Won't Start
- Check if port 5000 is already in use
- Ensure all dependencies are installed: `cd server && npm install`
- Check for syntax errors in the console

### Frontend API Connection Issues
- Verify backend is running on port 5000
- Check browser console for CORS errors
- Ensure `.env.local` has correct API URL

## 📊 Expected Test Results

### Successful Backend Test Output:
```
🚀 Testing PropertyPro API Endpoints...

🔄 Testing health endpoint...
✅ Health check: OK
   Uptime: 45 seconds
   Environment: development

🔄 Testing authentication endpoints...
✅ User registration successful
   User ID: abc-123-def
   Token received: Yes

🔄 Testing login...
✅ Login successful
   User: Admin User
   Role: landlord

🔄 Testing protected endpoint...
✅ Protected endpoint access successful
   Current user: Admin User

🔄 Testing dashboard stats...
✅ Dashboard stats retrieved
   Total Properties: 0
   Total Units: 0
   Occupancy Rate: 0%

🔄 Testing properties endpoint...
✅ Properties endpoint working
   Properties found: 0
   Pagination: {page: 1, limit: 10, total: 0, totalPages: 0}

🎉 API testing completed!
```

## 🎯 Next Steps After Successful Testing

1. **Complete remaining API endpoints** (invoices, payments, maintenance)
2. **Add comprehensive error handling**
3. **Implement file upload for property images**
4. **Add real-time notifications**
5. **Create comprehensive test suite**

## 📱 Mobile Responsiveness Testing

Test the frontend on different screen sizes:
- Desktop (1920x1080)
- Tablet (768x1024)
- Mobile (375x667)

Key areas to test:
- Navigation menu (should collapse on mobile)
- Dashboard cards (should stack on mobile)
- Property listings (should be touch-friendly)
- Forms (should be easy to use on mobile)

---

**Note:** If you encounter any issues during testing, please check the console logs for detailed error messages and refer to the troubleshooting section above.
