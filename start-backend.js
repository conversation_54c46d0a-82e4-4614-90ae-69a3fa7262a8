// Simple script to start the backend server
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting PropertyPro Backend Server...\n');

// Change to server directory
const serverDir = path.join(__dirname, 'server');

// Start the server using tsx
const serverProcess = spawn('npx', ['tsx', 'src/server.ts'], {
  cwd: serverDir,
  stdio: 'inherit',
  shell: true
});

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
});

serverProcess.on('close', (code) => {
  console.log(`\n🛑 Server process exited with code ${code}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});

console.log('✅ Server starting...');
console.log('📍 Backend will be available at: http://localhost:5000');
console.log('📍 API endpoints at: http://localhost:5000/api/v1');
console.log('\n💡 Test the server:');
console.log('   - Health check: http://localhost:5000/api/v1/health');
console.log('   - Login: POST http://localhost:5000/api/v1/auth/login');
console.log('\n🔑 Login credentials:');
console.log('   - Email: <EMAIL>');
console.log('   - Password: password123');
console.log('\n⏹️  Press Ctrl+C to stop the server');
