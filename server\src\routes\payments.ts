import { Router, Request, Response } from 'express';
import { PaymentModel } from '../models/Payment.js';
import { InvoiceModel } from '../models/Invoice.js';
import { TenantModel } from '../models/Tenant.js';
import { authorizeRoles } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { Payment } from '../types/index.js';

const router = Router();

// Get all payments
router.get('/',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      sortBy = 'payment_date',
      sortOrder = 'desc',
      search,
      propertyId,
      status,
      paymentMethod,
      dateFrom,
      dateTo
    } = req.query as any;

    const options: any = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      propertyId,
      status,
      paymentMethod,
      dateFrom: dateFrom ? new Date(dateFrom) : undefined,
      dateTo: dateTo ? new Date(dateTo) : undefined
    };

    // Filter based on user role
    if (req.user!.role === 'landlord') {
      options.landlordId = req.user!.id;
    } else if (req.user!.role === 'tenant') {
      const tenant = await TenantModel.findByUserId(req.user!.id);
      if (tenant) {
        options.tenantId = tenant.id;
      }
    }

    const { payments, total } = await PaymentModel.findAll(options);

    res.json({
      success: true,
      data: payments,
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages: Math.ceil(total / options.limit)
      }
    });
  })
);

// Get payment by ID
router.get('/:id',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const payment = await PaymentModel.findById(id);

    if (!payment) {
      throw new AppError('Payment not found', 404);
    }

    // Check permissions
    if (req.user!.role === 'tenant') {
      const tenant = await TenantModel.findByUserId(req.user!.id);
      if (!tenant || tenant.id !== payment.tenant_id) {
        throw new AppError('Access denied', 403);
      }
    }

    res.json({
      success: true,
      data: payment
    });
  })
);

// Create new payment
router.post('/',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  validate(schemas.createPayment),
  asyncHandler(async (req: Request, res: Response) => {
    const paymentData: Partial<Payment> = req.body;

    // Validate invoice exists
    if (paymentData.invoice_id) {
      const invoice = await InvoiceModel.findById(paymentData.invoice_id);
      if (!invoice) {
        throw new AppError('Invoice not found', 404);
      }

      // Auto-set tenant_id from invoice if not provided
      if (!paymentData.tenant_id) {
        paymentData.tenant_id = invoice.tenant_id;
      }

      // Check if invoice is already paid
      if (invoice.status === 'paid') {
        throw new AppError('Invoice is already paid', 400);
      }
    }

    // Check permissions for tenant
    if (req.user!.role === 'tenant') {
      const tenant = await TenantModel.findByUserId(req.user!.id);
      if (!tenant || tenant.id !== paymentData.tenant_id) {
        throw new AppError('Access denied', 403);
      }
    }

    const payment = await PaymentModel.create(paymentData);

    res.status(201).json({
      success: true,
      message: 'Payment created successfully',
      data: payment
    });
  })
);

// Process M-Pesa payment
router.post('/mpesa',
  authorizeRoles('tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { invoice_id, amount, phone_number } = req.body;

    if (!invoice_id || !amount || !phone_number) {
      throw new AppError('Invoice ID, amount, and phone number are required', 400);
    }

    // Validate invoice
    const invoice = await InvoiceModel.findById(invoice_id);
    if (!invoice) {
      throw new AppError('Invoice not found', 404);
    }

    // Check permissions
    const tenant = await TenantModel.findByUserId(req.user!.id);
    if (!tenant || tenant.id !== invoice.tenant_id) {
      throw new AppError('Access denied', 403);
    }

    // Validate amount
    if (amount !== invoice.total_amount) {
      throw new AppError('Payment amount must match invoice total', 400);
    }

    const payment = await PaymentModel.processMpesaPayment({
      invoice_id,
      tenant_id: tenant.id,
      amount,
      phone_number
    });

    res.status(201).json({
      success: true,
      message: 'M-Pesa payment initiated successfully',
      data: payment
    });
  })
);

// Process bank transfer
router.post('/bank-transfer',
  authorizeRoles('tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { invoice_id, amount, reference_number, bank_name } = req.body;

    if (!invoice_id || !amount || !reference_number) {
      throw new AppError('Invoice ID, amount, and reference number are required', 400);
    }

    // Validate invoice
    const invoice = await InvoiceModel.findById(invoice_id);
    if (!invoice) {
      throw new AppError('Invoice not found', 404);
    }

    // Check permissions
    const tenant = await TenantModel.findByUserId(req.user!.id);
    if (!tenant || tenant.id !== invoice.tenant_id) {
      throw new AppError('Access denied', 403);
    }

    const payment = await PaymentModel.processBankTransfer({
      invoice_id,
      tenant_id: tenant.id,
      amount,
      reference_number,
      bank_name
    });

    res.status(201).json({
      success: true,
      message: 'Bank transfer payment recorded successfully',
      data: payment
    });
  })
);

// Process card payment
router.post('/card',
  authorizeRoles('tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { invoice_id, amount, card_token, gateway } = req.body;

    if (!invoice_id || !amount || !card_token || !gateway) {
      throw new AppError('Invoice ID, amount, card token, and gateway are required', 400);
    }

    // Validate invoice
    const invoice = await InvoiceModel.findById(invoice_id);
    if (!invoice) {
      throw new AppError('Invoice not found', 404);
    }

    // Check permissions
    const tenant = await TenantModel.findByUserId(req.user!.id);
    if (!tenant || tenant.id !== invoice.tenant_id) {
      throw new AppError('Access denied', 403);
    }

    const payment = await PaymentModel.processCardPayment({
      invoice_id,
      tenant_id: tenant.id,
      amount,
      card_token,
      gateway
    });

    res.status(201).json({
      success: true,
      message: 'Card payment processed successfully',
      data: payment
    });
  })
);

// Update payment status (for webhooks and manual updates)
router.put('/:id/status',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { status, gateway_transaction_id, reference_number, notes } = req.body;

    if (!status) {
      throw new AppError('Status is required', 400);
    }

    const payment = await PaymentModel.findById(id);
    if (!payment) {
      throw new AppError('Payment not found', 404);
    }

    const updatedPayment = await PaymentModel.update(id, {
      status,
      gateway_transaction_id,
      reference_number,
      notes
    });

    res.json({
      success: true,
      message: 'Payment status updated successfully',
      data: updatedPayment
    });
  })
);

// Reconcile payments
router.post('/reconcile',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { payment_ids, bank_statement_reference } = req.body;

    if (!payment_ids || !Array.isArray(payment_ids) || payment_ids.length === 0) {
      throw new AppError('Payment IDs array is required', 400);
    }

    if (!bank_statement_reference) {
      throw new AppError('Bank statement reference is required', 400);
    }

    const success = await PaymentModel.reconcilePayments({
      payment_ids,
      bank_statement_reference,
      reconciled_by: req.user!.id
    });

    res.json({
      success: true,
      message: `Successfully reconciled ${payment_ids.length} payments`,
      data: { reconciled_count: payment_ids.length }
    });
  })
);

// Get payment statistics
router.get('/stats/summary',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { dateFrom, dateTo } = req.query as any;

    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    const stats = await PaymentModel.getStats(
      landlordId,
      dateFrom ? new Date(dateFrom) : undefined,
      dateTo ? new Date(dateTo) : undefined
    );

    res.json({
      success: true,
      data: stats
    });
  })
);

// Get payment methods statistics
router.get('/stats/methods',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    const methodsStats = await PaymentModel.getPaymentMethodsStats(landlordId);

    res.json({
      success: true,
      data: methodsStats
    });
  })
);

export default router;
