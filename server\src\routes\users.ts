import { Router, Request, Response } from 'express';
import { UserModel } from '../models/User.js';
import { authorizeRoles } from '../middleware/auth.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';

const router = Router();

// Get all users (admin/landlord only)
router.get('/',
  authorizeR<PERSON>s('landlord'),
  asyncHandler(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      role,
      search
    } = req.query as any;
    
    const { users, total } = await UserModel.findAll(
      parseInt(page),
      parseInt(limit),
      role,
      search
    );
    
    res.json({
      success: true,
      data: users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  })
);

// Get user by ID
router.get('/:id',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    const user = await UserModel.findById(id);
    
    if (!user) {
      throw new AppError('User not found', 404);
    }
    
    res.json({
      success: true,
      data: user
    });
  })
);

export default router;
