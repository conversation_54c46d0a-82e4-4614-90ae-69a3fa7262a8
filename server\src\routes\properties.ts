import { Router, Request, Response } from 'express';
import { PropertyModel } from '../models/Property.js';
import { UnitModel } from '../models/Unit.js';
import { authorizeRoles } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { CreatePropertyRequest, PaginationQuery } from '../types/index.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(process.cwd(), 'uploads', 'properties');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `property-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Get all properties
router.get('/',
  asyncHandler(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      propertyType,
      city
    } = req.query as any;
    
    const options: PaginationQuery & {
      landlordId?: string;
      managerId?: string;
      propertyType?: string;
      city?: string;
    } = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      propertyType,
      city
    };
    
    // Filter based on user role
    if (req.user!.role === 'landlord') {
      options.landlordId = req.user!.id;
    } else if (req.user!.role === 'property_manager') {
      options.managerId = req.user!.id;
    }
    
    const { properties, total } = await PropertyModel.findAll(options);
    
    res.json({
      success: true,
      data: properties,
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages: Math.ceil(total / options.limit)
      }
    });
  })
);

// Get property by ID
router.get('/:id',
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    const property = await PropertyModel.findById(id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    // Check if user has access to this property
    if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    res.json({
      success: true,
      data: property
    });
  })
);

// Create new property with image upload
router.post('/',
  authorizeRoles('landlord'),
  upload.array('images', 10),
  validate(schemas.createProperty),
  asyncHandler(async (req: Request, res: Response) => {
    const propertyData: CreatePropertyRequest = req.body;
    const landlordId = req.user!.id;

    // Handle uploaded images
    const files = req.files as Express.Multer.File[];
    if (files && files.length > 0) {
      propertyData.images = files.map(file => `/uploads/properties/${file.filename}`);
    }

    // Parse amenities if it's a string
    if (typeof propertyData.amenities === 'string') {
      try {
        propertyData.amenities = JSON.parse(propertyData.amenities);
      } catch (error) {
        propertyData.amenities = [];
      }
    }

    const property = await PropertyModel.create(propertyData, landlordId);

    res.status(201).json({
      success: true,
      message: 'Property created successfully',
      data: property
    });
  })
);

// Update property
router.put('/:id',
  authorizeRoles('landlord', 'property_manager'),
  validate(schemas.updateProperty),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;
    
    // Check if property exists and user has access
    const existingProperty = await PropertyModel.findById(id);
    
    if (!existingProperty) {
      throw new AppError('Property not found', 404);
    }
    
    // Check permissions
    if (req.user!.role === 'landlord' && existingProperty.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && existingProperty.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    // Property managers can't change certain fields
    if (req.user!.role === 'property_manager') {
      delete updateData.landlord_id;
      delete updateData.manager_id;
      delete updateData.is_active;
    }
    
    const updatedProperty = await PropertyModel.update(id, updateData);
    
    res.json({
      success: true,
      message: 'Property updated successfully',
      data: updatedProperty
    });
  })
);

// Delete property (soft delete)
router.delete('/:id',
  authorizeRoles('landlord'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    // Check if property exists and user owns it
    const property = await PropertyModel.findById(id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    if (property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    const deleted = await PropertyModel.delete(id);
    
    if (!deleted) {
      throw new AppError('Failed to delete property', 500);
    }
    
    res.json({
      success: true,
      message: 'Property deleted successfully'
    });
  })
);

// Get property statistics
router.get('/:id/stats',
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    // Check if property exists and user has access
    const property = await PropertyModel.findById(id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    // Check permissions
    if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    // Get property stats (this would need to be implemented in PropertyModel)
    const stats = {
      totalUnits: property.total_units || 0,
      occupiedUnits: property.occupied_units || 0,
      vacantUnits: (property.total_units || 0) - (property.occupied_units || 0),
      occupancyRate: property.total_units ? 
        Math.round(((property.occupied_units || 0) / property.total_units) * 100) : 0
    };
    
    res.json({
      success: true,
      data: stats
    });
  })
);

export default router;
