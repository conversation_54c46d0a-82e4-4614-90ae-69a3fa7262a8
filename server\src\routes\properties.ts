import { Router, Request, Response } from 'express';
import { PropertyModel } from '../models/Property.js';
import { authorizeRoles } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { CreatePropertyRequest, PaginationQuery } from '../types/index.js';

const router = Router();

// Get all properties
router.get('/',
  asyncHandler(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      propertyType,
      city
    } = req.query as any;
    
    const options: PaginationQuery & {
      landlordId?: string;
      managerId?: string;
      propertyType?: string;
      city?: string;
    } = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      propertyType,
      city
    };
    
    // Filter based on user role
    if (req.user!.role === 'landlord') {
      options.landlordId = req.user!.id;
    } else if (req.user!.role === 'property_manager') {
      options.managerId = req.user!.id;
    }
    
    const { properties, total } = await PropertyModel.findAll(options);
    
    res.json({
      success: true,
      data: properties,
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages: Math.ceil(total / options.limit)
      }
    });
  })
);

// Get property by ID
router.get('/:id',
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    const property = await PropertyModel.findById(id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    // Check if user has access to this property
    if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    res.json({
      success: true,
      data: property
    });
  })
);

// Create new property
router.post('/',
  authorizeRoles('landlord'),
  validate(schemas.createProperty),
  asyncHandler(async (req: Request, res: Response) => {
    const propertyData: CreatePropertyRequest = req.body;
    const landlordId = req.user!.id;
    
    const property = await PropertyModel.create(propertyData, landlordId);
    
    res.status(201).json({
      success: true,
      message: 'Property created successfully',
      data: property
    });
  })
);

// Update property
router.put('/:id',
  authorizeRoles('landlord', 'property_manager'),
  validate(schemas.updateProperty),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;
    
    // Check if property exists and user has access
    const existingProperty = await PropertyModel.findById(id);
    
    if (!existingProperty) {
      throw new AppError('Property not found', 404);
    }
    
    // Check permissions
    if (req.user!.role === 'landlord' && existingProperty.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && existingProperty.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    // Property managers can't change certain fields
    if (req.user!.role === 'property_manager') {
      delete updateData.landlord_id;
      delete updateData.manager_id;
      delete updateData.is_active;
    }
    
    const updatedProperty = await PropertyModel.update(id, updateData);
    
    res.json({
      success: true,
      message: 'Property updated successfully',
      data: updatedProperty
    });
  })
);

// Delete property (soft delete)
router.delete('/:id',
  authorizeRoles('landlord'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    // Check if property exists and user owns it
    const property = await PropertyModel.findById(id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    if (property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    const deleted = await PropertyModel.delete(id);
    
    if (!deleted) {
      throw new AppError('Failed to delete property', 500);
    }
    
    res.json({
      success: true,
      message: 'Property deleted successfully'
    });
  })
);

// Get property statistics
router.get('/:id/stats',
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    // Check if property exists and user has access
    const property = await PropertyModel.findById(id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    // Check permissions
    if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    // Get property stats (this would need to be implemented in PropertyModel)
    const stats = {
      totalUnits: property.total_units || 0,
      occupiedUnits: property.occupied_units || 0,
      vacantUnits: (property.total_units || 0) - (property.occupied_units || 0),
      occupancyRate: property.total_units ? 
        Math.round(((property.occupied_units || 0) / property.total_units) * 100) : 0
    };
    
    res.json({
      success: true,
      data: stats
    });
  })
);

export default router;
