import { UserModel } from '../models/User.js';
import { generateToken } from '../middleware/auth.js';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testAuthentication() {
  console.log('🔐 Testing Authentication Flow...\n');

  try {
    // Test 1: Check if admin user exists
    console.log('1. Checking if admin user exists...');
    const adminUser = await UserModel.findByEmail('<EMAIL>');
    
    if (!adminUser) {
      console.log('❌ Admin user not found!');
      console.log('Creating admin user...');
      
      const newAdmin = await UserModel.create({
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Admin',
        last_name: 'User',
        role: 'landlord'
      });
      
      console.log('✅ Admin user created:', newAdmin.email, 'Role:', newAdmin.role);
    } else {
      console.log('✅ Admin user found:', adminUser.email, 'Role:', adminUser.role);
    }

    // Test 2: Test password verification
    console.log('\n2. Testing password verification...');
    const user = await UserModel.findByEmail('<EMAIL>');
    if (user) {
      const isPasswordValid = await UserModel.verifyPassword('password123', user.password_hash!);
      console.log('✅ Password verification:', isPasswordValid ? 'VALID' : 'INVALID');
    }

    // Test 3: Test JWT token generation
    console.log('\n3. Testing JWT token generation...');
    if (user) {
      const token = generateToken(user);
      console.log('✅ JWT Token generated:', token.substring(0, 50) + '...');
      
      // Test token verification
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { id: string };
        console.log('✅ Token verification successful. User ID:', decoded.id);
        
        // Test user lookup from token
        const tokenUser = await UserModel.findById(decoded.id);
        if (tokenUser) {
          console.log('✅ User lookup from token successful:', tokenUser.email, 'Role:', tokenUser.role);
        } else {
          console.log('❌ User lookup from token failed');
        }
      } catch (error) {
        console.log('❌ Token verification failed:', error);
      }
    }

    // Test 4: Test role authorization
    console.log('\n4. Testing role authorization...');
    if (user) {
      const allowedRoles = ['landlord', 'property_manager'];
      const hasAccess = allowedRoles.includes(user.role);
      console.log('✅ Role authorization for properties:', hasAccess ? 'ALLOWED' : 'DENIED');
      console.log('   User role:', user.role);
      console.log('   Allowed roles:', allowedRoles.join(', '));
    }

    // Test 5: Check environment variables
    console.log('\n5. Checking environment variables...');
    console.log('✅ JWT_SECRET exists:', !!process.env.JWT_SECRET);
    console.log('✅ JWT_SECRET length:', process.env.JWT_SECRET?.length || 0);
    console.log('✅ Database connection configured:', !!process.env.DB_NAME);

    // Test 6: Test all user roles
    console.log('\n6. Testing all user roles...');
    const allUsers = await UserModel.findAll(1, 10);
    console.log('✅ Total users found:', allUsers.total);
    
    allUsers.users.forEach(user => {
      console.log(`   - ${user.email}: ${user.role} (Active: ${user.is_active})`);
    });

    console.log('\n🎉 Authentication test completed successfully!');
    
    // Test 7: Simulate API request flow
    console.log('\n7. Simulating API request flow...');
    if (user) {
      const token = generateToken(user);
      
      // Simulate what happens in the authenticateToken middleware
      try {
        const authHeader = `Bearer ${token}`;
        const extractedToken = authHeader.split(' ')[1];
        const decoded = jwt.verify(extractedToken, process.env.JWT_SECRET as string) as { id: string };
        const requestUser = await UserModel.findById(decoded.id);
        
        if (requestUser) {
          console.log('✅ API request simulation successful');
          console.log('   Token extracted correctly');
          console.log('   User found:', requestUser.email);
          console.log('   User role:', requestUser.role);
          
          // Test authorization for different endpoints
          const endpoints = [
            { path: '/properties', roles: ['landlord', 'property_manager'] },
            { path: '/tenants', roles: ['landlord', 'property_manager'] },
            { path: '/invoices', roles: ['landlord', 'property_manager'] },
            { path: '/payments', roles: ['landlord', 'property_manager'] }
          ];
          
          endpoints.forEach(endpoint => {
            const hasAccess = endpoint.roles.includes(requestUser.role);
            console.log(`   ${endpoint.path}: ${hasAccess ? '✅ ALLOWED' : '❌ DENIED'}`);
          });
        } else {
          console.log('❌ API request simulation failed: User not found');
        }
      } catch (error) {
        console.log('❌ API request simulation failed:', error);
      }
    }

  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testAuthentication()
    .then(() => {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

export { testAuthentication };
