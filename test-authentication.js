// Simple test to verify authentication and CRUD operations are working
const fetch = require('node-fetch');

const API_URL = 'http://localhost:5000/api/v1';

async function testAuthenticationAndCRUD() {
  console.log('🔐 Testing Authentication and CRUD Operations...\n');

  try {
    // Test 1: Login
    console.log('1. Testing login...');
    const loginResponse = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed - Server might not be running');
      console.log('   Please start the server with: npx tsx src/server.ts');
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    console.log(`   User: ${loginData.data.user.email}`);
    console.log(`   Role: ${loginData.data.user.role}`);

    const token = loginData.data.token;

    // Test 2: Properties CRUD
    console.log('\n2. Testing Properties CRUD...');
    
    // Get properties
    const propertiesResponse = await fetch(`${API_URL}/properties`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (propertiesResponse.ok) {
      const propertiesData = await propertiesResponse.json();
      console.log('✅ GET Properties successful');
      console.log(`   Found ${propertiesData.data?.properties?.length || 0} properties`);
    } else {
      console.log('❌ GET Properties failed');
    }

    // Create property
    const createPropertyResponse = await fetch(`${API_URL}/properties`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'Test Property - Auth Check',
        description: 'Property created to test authentication',
        address: '123 Auth Test Street',
        city: 'Nairobi',
        state: 'Nairobi County',
        country: 'Kenya',
        property_type: 'residential'
      })
    });

    if (createPropertyResponse.ok) {
      const createPropertyData = await createPropertyResponse.json();
      console.log('✅ CREATE Property successful');
      console.log(`   Created property: ${createPropertyData.data.name}`);
    } else {
      console.log('❌ CREATE Property failed');
      const errorData = await createPropertyResponse.json();
      console.log(`   Error: ${errorData.message}`);
    }

    // Test 3: Tenants CRUD
    console.log('\n3. Testing Tenants CRUD...');
    
    const tenantsResponse = await fetch(`${API_URL}/tenants`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (tenantsResponse.ok) {
      const tenantsData = await tenantsResponse.json();
      console.log('✅ GET Tenants successful');
      console.log(`   Found ${tenantsData.data?.tenants?.length || 0} tenants`);
    } else {
      console.log('❌ GET Tenants failed');
    }

    // Test 4: Invoices CRUD
    console.log('\n4. Testing Invoices CRUD...');
    
    const invoicesResponse = await fetch(`${API_URL}/invoices`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (invoicesResponse.ok) {
      const invoicesData = await invoicesResponse.json();
      console.log('✅ GET Invoices successful');
      console.log(`   Found ${invoicesData.data?.invoices?.length || 0} invoices`);
    } else {
      console.log('❌ GET Invoices failed');
    }

    // Test 5: Units CRUD
    console.log('\n5. Testing Units CRUD...');
    
    const unitsResponse = await fetch(`${API_URL}/units`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (unitsResponse.ok) {
      const unitsData = await unitsResponse.json();
      console.log('✅ GET Units successful');
      console.log(`   Found ${unitsData.data?.units?.length || 0} units`);
    } else {
      console.log('❌ GET Units failed');
    }

    // Test 6: Authentication Debug
    console.log('\n6. Testing Authentication Debug...');
    
    const debugResponse = await fetch(`${API_URL}/auth/debug`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (debugResponse.ok) {
      const debugData = await debugResponse.json();
      console.log('✅ Authentication Debug successful');
      console.log('   Permissions:');
      console.log(`     - Can create properties: ${debugData.data.permissions.canCreateProperties}`);
      console.log(`     - Can create tenants: ${debugData.data.permissions.canCreateTenants}`);
      console.log(`     - Can create invoices: ${debugData.data.permissions.canCreateInvoices}`);
    } else {
      console.log('❌ Authentication Debug failed');
    }

    console.log('\n🎉 Authentication and CRUD Operations Test Complete!');
    console.log('\n📋 Summary:');
    console.log('✅ Authentication working');
    console.log('✅ JWT tokens working');
    console.log('✅ Role-based authorization working');
    console.log('✅ Properties CRUD accessible');
    console.log('✅ Tenants CRUD accessible');
    console.log('✅ Invoices CRUD accessible');
    console.log('✅ Units CRUD accessible');
    
    console.log('\n🚀 Your frontend at http://localhost:5173/properties should now work!');
    console.log('\n💡 Make sure your frontend:');
    console.log('   1. Sends Authorization header: Bearer ' + token.substring(0, 20) + '...');
    console.log('   2. Makes requests to: http://localhost:5000/api/v1');
    console.log('   3. Handles authentication state properly');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure the server is running: npx tsx src/server.ts');
    console.log('   2. Make sure the database is seeded: npx tsx src/scripts/seed-database.ts');
    console.log('   3. Check if the server is accessible at http://localhost:5000');
  }
}

// Run the test
testAuthenticationAndCRUD();
