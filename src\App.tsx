import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './hooks/useAuth';
import Layout from './components/Layout/Layout';
import LoginPage from './components/Auth/LoginPage';
import EnhancedDashboard from './components/Dashboard/EnhancedDashboard';
import PropertiesPage from './components/Properties/PropertiesPage';
import MaintenancePage from './components/Maintenance/MaintenancePage';
import InvoicesPage from './components/Invoices/InvoicesPage';

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  return user ? <>{children}</> : <Navigate to="/login" />;
};

const AppRoutes = () => {
  const { user } = useAuth();
  
  return (
    <Routes>
      <Route path="/login" element={!user ? <LoginPage /> : <Navigate to="/dashboard" />} />
      <Route path="/" element={<ProtectedRoute><Layout /></ProtectedRoute>}>
        <Route index element={<Navigate to="/dashboard" />} />
        <Route path="dashboard" element={<EnhancedDashboard />} />
        <Route path="properties" element={<PropertiesPage />} />
        <Route path="maintenance" element={<MaintenancePage />} />
        <Route path="invoices" element={<InvoicesPage />} />
        <Route path="tenants" element={<div className="p-6">Tenants Page - Coming Soon</div>} />
        <Route path="leases" element={<div className="p-6">Leases Page - Coming Soon</div>} />
        <Route path="payments" element={<div className="p-6">Payments Page - Coming Soon</div>} />
        <Route path="reports" element={<div className="p-6">Reports Page - Coming Soon</div>} />
        <Route path="my-invoices" element={<div className="p-6">My Invoices Page - Coming Soon</div>} />
        <Route path="my-payments" element={<div className="p-6">My Payments Page - Coming Soon</div>} />
        <Route path="my-maintenance" element={<div className="p-6">My Maintenance Page - Coming Soon</div>} />
        <Route path="my-lease" element={<div className="p-6">My Lease Page - Coming Soon</div>} />
      </Route>
    </Routes>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppRoutes />
      </Router>
    </AuthProvider>
  );
}

export default App;