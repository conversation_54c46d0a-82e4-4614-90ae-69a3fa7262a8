import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './hooks/useAuth';
import Layout from './components/Layout/Layout';
import LoginPage from './components/Auth/LoginPage';
import DashboardRouter from './components/Dashboard/DashboardRouter';
import PropertiesPage from './components/Properties/PropertiesPage';
import MaintenancePage from './components/Maintenance/MaintenancePage';
import InvoicesPage from './components/Invoices/InvoicesPage';
import TenantsPage from './components/Tenants/TenantsPage';
import LeasesPage from './components/Leases/LeasesPage';
import PaymentsPage from './components/Payments/PaymentsPage';
import ReportsPage from './components/Reports/ReportsPage';
import RoleBasedRoute from './components/Auth/RoleBasedRoute';

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  return user ? <>{children}</> : <Navigate to="/login" />;
};

const AppRoutes = () => {
  const { user } = useAuth();
  
  return (
    <Routes>
      <Route path="/login" element={!user ? <LoginPage /> : <Navigate to="/dashboard" />} />
      <Route path="/" element={<ProtectedRoute><Layout /></ProtectedRoute>}>
        <Route index element={<Navigate to="/dashboard" />} />
        <Route path="dashboard" element={<DashboardRouter />} />

        {/* Properties Management */}
        <Route path="properties" element={
          <RoleBasedRoute allowedRoles={['landlord', 'property_manager']}>
            <PropertiesPage />
          </RoleBasedRoute>
        } />

        {/* Tenant Management */}
        <Route path="tenants" element={
          <RoleBasedRoute allowedRoles={['landlord', 'property_manager']}>
            <TenantsPage />
          </RoleBasedRoute>
        } />

        {/* Lease Management */}
        <Route path="leases" element={
          <RoleBasedRoute allowedRoles={['landlord', 'property_manager']}>
            <LeasesPage />
          </RoleBasedRoute>
        } />

        {/* Invoice Management */}
        <Route path="invoices" element={
          <RoleBasedRoute allowedRoles={['landlord', 'property_manager']}>
            <InvoicesPage />
          </RoleBasedRoute>
        } />

        {/* Payment Management */}
        <Route path="payments" element={
          <RoleBasedRoute allowedRoles={['landlord', 'property_manager']}>
            <PaymentsPage />
          </RoleBasedRoute>
        } />

        {/* Maintenance Management */}
        <Route path="maintenance" element={
          <RoleBasedRoute allowedRoles={['landlord', 'property_manager', 'caretaker']}>
            <MaintenancePage />
          </RoleBasedRoute>
        } />

        {/* Reports */}
        <Route path="reports" element={
          <RoleBasedRoute allowedRoles={['landlord', 'property_manager']}>
            <ReportsPage />
          </RoleBasedRoute>
        } />

        {/* Tenant-specific routes */}
        <Route path="my-invoices" element={
          <RoleBasedRoute allowedRoles={['tenant']}>
            <InvoicesPage />
          </RoleBasedRoute>
        } />
        <Route path="my-payments" element={
          <RoleBasedRoute allowedRoles={['tenant']}>
            <PaymentsPage />
          </RoleBasedRoute>
        } />
        <Route path="my-maintenance" element={
          <RoleBasedRoute allowedRoles={['tenant']}>
            <MaintenancePage />
          </RoleBasedRoute>
        } />
        <Route path="my-lease" element={
          <RoleBasedRoute allowedRoles={['tenant']}>
            <LeasesPage />
          </RoleBasedRoute>
        } />
      </Route>
    </Routes>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppRoutes />
      </Router>
    </AuthProvider>
  );
}

export default App;