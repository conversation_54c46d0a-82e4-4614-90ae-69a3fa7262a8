import { executeQuery, executeTransaction } from '../config/database';
import { Invoice, InvoiceLineItem } from '../types';
import { format } from 'date-fns';

export class InvoiceService {
  // Generate monthly rent invoices
  static async generateMonthlyRentInvoices(month: number, year: number): Promise<string[]> {
    const query = `
      SELECT l.id as lease_id, l.tenant_id, l.monthly_rent, l.unit_id,
             u.unit_number, p.name as property_name,
             CONCAT(usr.first_name, ' ', usr.last_name) as tenant_name
      FROM leases l
      JOIN units u ON l.unit_id = u.id
      JOIN properties p ON u.property_id = p.id
      JOIN tenants t ON l.tenant_id = t.id
      JOIN users usr ON t.user_id = usr.id
      WHERE l.status = 'active'
        AND l.start_date <= LAST_DAY(STR_TO_DATE(CONCAT(?, '-', ?), '%Y-%m'))
        AND (l.end_date IS NULL OR l.end_date >= STR_TO_DATE(CONCAT(?, '-', ?), '%Y-%m'))
    `;
    
    const leases = await executeQuery(query, [year, month, year, month]);
    const invoiceIds: string[] = [];
    
    for (const lease of leases) {
      // Check if invoice already exists for this month
      const existingInvoice = await this.getInvoiceByLeaseAndMonth(lease.lease_id, month, year);
      
      if (!existingInvoice) {
        const invoiceNumber = await this.generateInvoiceNumber();
        const dueDate = new Date(year, month, 1); // First day of the month
        const issueDate = new Date();
        
        const lineItems: InvoiceLineItem[] = [{
          description: `Rent for ${lease.property_name} - Unit ${lease.unit_number}`,
          quantity: 1,
          unitPrice: parseFloat(lease.monthly_rent),
          total: parseFloat(lease.monthly_rent)
        }];
        
        const invoiceId = await this.createInvoice({
          invoiceNumber,
          leaseId: lease.lease_id,
          tenantId: lease.tenant_id,
          type: 'rent',
          amount: parseFloat(lease.monthly_rent),
          taxAmount: 0,
          totalAmount: parseFloat(lease.monthly_rent),
          dueDate,
          issueDate,
          description: `Monthly rent for ${format(dueDate, 'MMMM yyyy')}`,
          lineItems,
          status: 'sent'
        });
        
        invoiceIds.push(invoiceId);
      }
    }
    
    return invoiceIds;
  }

  // Create invoice
  static async createInvoice(invoiceData: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const query = `
      INSERT INTO invoices (invoice_number, lease_id, tenant_id, invoice_type, amount,
                           tax_amount, total_amount, due_date, issue_date, description,
                           line_items, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      invoiceData.invoiceNumber,
      invoiceData.leaseId,
      invoiceData.tenantId,
      invoiceData.type,
      invoiceData.amount,
      invoiceData.taxAmount,
      invoiceData.totalAmount,
      invoiceData.dueDate,
      invoiceData.issueDate,
      invoiceData.description,
      JSON.stringify(invoiceData.lineItems),
      invoiceData.status
    ];
    
    const result = await executeQuery(query, params);
    return result.insertId;
  }

  // Get invoices by tenant
  static async getInvoicesByTenant(tenantId: string, limit: number = 50): Promise<Invoice[]> {
    const query = `
      SELECT i.*, 
             u.unit_number,
             p.name as property_name,
             p.address as property_address
      FROM invoices i
      JOIN leases l ON i.lease_id = l.id
      JOIN units u ON l.unit_id = u.id
      JOIN properties p ON u.property_id = p.id
      WHERE i.tenant_id = ?
      ORDER BY i.created_at DESC
      LIMIT ?
    `;
    
    const results = await executeQuery(query, [tenantId, limit]);
    return results.map(this.mapDatabaseToInvoice);
  }

  // Get overdue invoices
  static async getOverdueInvoices(): Promise<Invoice[]> {
    const query = `
      SELECT i.*, 
             u.unit_number,
             p.name as property_name,
             CONCAT(usr.first_name, ' ', usr.last_name) as tenant_name,
             usr.email as tenant_email,
             usr.phone as tenant_phone,
             DATEDIFF(CURDATE(), i.due_date) as days_overdue
      FROM invoices i
      JOIN leases l ON i.lease_id = l.id
      JOIN units u ON l.unit_id = u.id
      JOIN properties p ON u.property_id = p.id
      JOIN tenants t ON i.tenant_id = t.id
      JOIN users usr ON t.user_id = usr.id
      WHERE i.status IN ('sent', 'viewed', 'partial')
        AND i.due_date < CURDATE()
      ORDER BY i.due_date ASC
    `;
    
    const results = await executeQuery(query);
    return results.map(this.mapDatabaseToInvoice);
  }

  // Apply late fees to overdue invoices
  static async applyLateFees(): Promise<number> {
    const overdueInvoices = await this.getOverdueInvoices();
    let updatedCount = 0;
    
    for (const invoice of overdueInvoices) {
      // Get lease details for late fee calculation
      const leaseQuery = `
        SELECT late_fee_amount, late_fee_type, grace_period_days
        FROM leases
        WHERE id = ?
      `;
      
      const [lease] = await executeQuery(leaseQuery, [invoice.leaseId]);
      
      if (lease && lease.late_fee_amount > 0) {
        const daysOverdue = Math.floor((Date.now() - invoice.dueDate.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysOverdue > lease.grace_period_days) {
          let lateFee = 0;
          
          if (lease.late_fee_type === 'percentage') {
            lateFee = (invoice.amount * lease.late_fee_amount) / 100;
          } else {
            lateFee = lease.late_fee_amount;
          }
          
          // Update invoice with late fee
          const updateQuery = `
            UPDATE invoices 
            SET late_fee_applied = ?, 
                total_amount = amount + tax_amount + ?,
                status = 'overdue'
            WHERE id = ? AND late_fee_applied = 0
          `;
          
          await executeQuery(updateQuery, [lateFee, lateFee, invoice.id]);
          updatedCount++;
        }
      }
    }
    
    return updatedCount;
  }

  // Get invoice by lease and month
  private static async getInvoiceByLeaseAndMonth(leaseId: string, month: number, year: number): Promise<Invoice | null> {
    const query = `
      SELECT * FROM invoices
      WHERE lease_id = ?
        AND MONTH(issue_date) = ?
        AND YEAR(issue_date) = ?
        AND invoice_type = 'rent'
    `;
    
    const [result] = await executeQuery(query, [leaseId, month, year]);
    return result ? this.mapDatabaseToInvoice(result) : null;
  }

  // Generate unique invoice number
  private static async generateInvoiceNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    const query = `
      SELECT COUNT(*) as count
      FROM invoices
      WHERE invoice_number LIKE ?
    `;
    
    const prefix = `INV-${year}${month}`;
    const [result] = await executeQuery(query, [`${prefix}%`]);
    const sequence = String(result.count + 1).padStart(4, '0');
    
    return `${prefix}-${sequence}`;
  }

  // Map database result to Invoice object
  private static mapDatabaseToInvoice(row: any): Invoice {
    return {
      id: row.id,
      invoiceNumber: row.invoice_number,
      leaseId: row.lease_id,
      tenantId: row.tenant_id,
      type: row.invoice_type,
      amount: parseFloat(row.amount),
      taxAmount: parseFloat(row.tax_amount || '0'),
      totalAmount: parseFloat(row.total_amount),
      dueDate: new Date(row.due_date),
      issueDate: new Date(row.issue_date),
      description: row.description,
      lineItems: row.line_items ? JSON.parse(row.line_items) : [],
      status: row.status,
      paidAmount: parseFloat(row.paid_amount || '0'),
      paymentDate: row.payment_date ? new Date(row.payment_date) : undefined,
      lateFee: parseFloat(row.late_fee_applied || '0'),
      pdfUrl: row.pdf_url,
      reminderSentCount: row.reminder_sent_count || 0,
      lastReminderSent: row.last_reminder_sent ? new Date(row.last_reminder_sent) : undefined,
      notes: row.notes,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      // Additional fields from joins
      unitNumber: row.unit_number,
      propertyName: row.property_name,
      tenantName: row.tenant_name,
      tenantEmail: row.tenant_email,
      tenantPhone: row.tenant_phone,
      daysOverdue: row.days_overdue
    };
  }
}