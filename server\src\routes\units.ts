import { Router, Request, Response } from 'express';
import { UnitModel } from '../models/Unit.js';
import { PropertyModel } from '../models/Property.js';
import { authorizeRoles } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { CreateUnitRequest } from '../types/index.js';

const router = Router();

// Get units by property ID
router.get('/property/:propertyId',
  asyncHandler(async (req: Request, res: Response) => {
    const { propertyId } = req.params;
    const {
      page = 1,
      limit = 10,
      sortBy = 'unit_number',
      sortOrder = 'asc',
      search
    } = req.query as any;
    
    // Check if property exists and user has access
    const property = await PropertyModel.findById(propertyId);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    // Check permissions
    if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search
    };
    
    const { units, total } = await UnitModel.findByPropertyId(propertyId, options);
    
    res.json({
      success: true,
      data: units,
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages: Math.ceil(total / options.limit)
      }
    });
  })
);

// Get unit by ID
router.get('/:id',
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    const unit = await UnitModel.findById(id);
    
    if (!unit) {
      throw new AppError('Unit not found', 404);
    }
    
    // Check if user has access to this unit's property
    const property = await PropertyModel.findById(unit.property_id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    // Check permissions
    if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    res.json({
      success: true,
      data: unit
    });
  })
);

// Create new unit
router.post('/',
  authorizeRoles('landlord', 'property_manager'),
  validate(schemas.createUnit),
  asyncHandler(async (req: Request, res: Response) => {
    const unitData: CreateUnitRequest = req.body;
    
    // Check if property exists and user has access
    const property = await PropertyModel.findById(unitData.property_id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    // Check permissions
    if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    // Check if unit number already exists in this property
    const unitExists = await UnitModel.unitNumberExists(unitData.property_id, unitData.unit_number);
    
    if (unitExists) {
      throw new AppError('Unit number already exists in this property', 409);
    }
    
    const unit = await UnitModel.create(unitData);
    
    res.status(201).json({
      success: true,
      message: 'Unit created successfully',
      data: unit
    });
  })
);

// Update unit
router.put('/:id',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;
    
    // Check if unit exists
    const existingUnit = await UnitModel.findById(id);
    
    if (!existingUnit) {
      throw new AppError('Unit not found', 404);
    }
    
    // Check if property exists and user has access
    const property = await PropertyModel.findById(existingUnit.property_id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    // Check permissions
    if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    // If updating unit number, check if it already exists
    if (updateData.unit_number && updateData.unit_number !== existingUnit.unit_number) {
      const unitExists = await UnitModel.unitNumberExists(
        existingUnit.property_id, 
        updateData.unit_number, 
        id
      );
      
      if (unitExists) {
        throw new AppError('Unit number already exists in this property', 409);
      }
    }
    
    const updatedUnit = await UnitModel.update(id, updateData);
    
    res.json({
      success: true,
      message: 'Unit updated successfully',
      data: updatedUnit
    });
  })
);

// Delete unit
router.delete('/:id',
  authorizeRoles('landlord'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    // Check if unit exists
    const unit = await UnitModel.findById(id);
    
    if (!unit) {
      throw new AppError('Unit not found', 404);
    }
    
    // Check if property exists and user owns it
    const property = await PropertyModel.findById(unit.property_id);
    
    if (!property) {
      throw new AppError('Property not found', 404);
    }
    
    if (property.landlord_id !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }
    
    // Check if unit is occupied
    if (unit.is_occupied) {
      throw new AppError('Cannot delete occupied unit', 400);
    }
    
    const deleted = await UnitModel.delete(id);
    
    if (!deleted) {
      throw new AppError('Failed to delete unit', 500);
    }
    
    res.json({
      success: true,
      message: 'Unit deleted successfully'
    });
  })
);

// Get available units
router.get('/available/list',
  asyncHandler(async (req: Request, res: Response) => {
    const { propertyId } = req.query as any;
    
    let units;
    
    if (propertyId) {
      // Check if property exists and user has access
      const property = await PropertyModel.findById(propertyId);
      
      if (!property) {
        throw new AppError('Property not found', 404);
      }
      
      // Check permissions
      if (req.user!.role === 'landlord' && property.landlord_id !== req.user!.id) {
        throw new AppError('Access denied', 403);
      }
      
      if (req.user!.role === 'property_manager' && property.manager_id !== req.user!.id) {
        throw new AppError('Access denied', 403);
      }
      
      units = await UnitModel.findAvailable(propertyId);
    } else {
      // Get all available units (filtered by user role)
      units = await UnitModel.findAvailable();
      
      // Filter based on user role
      if (req.user!.role === 'landlord') {
        // This would need additional logic to filter by landlord's properties
        // For now, we'll get all available units
      }
    }
    
    res.json({
      success: true,
      data: units
    });
  })
);

export default router;
