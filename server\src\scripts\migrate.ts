import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { pool, executeQuery } from '../config/database.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Helper function to get directory name
function dirname(path: string) {
  return new URL('.', path).pathname;
}

async function createDatabase() {
  try {
    console.log('🔄 Creating database if it doesn\'t exist...');
    
    // Create a connection without database selected
    const connection = await pool.getConnection();
    
    // Create database if it doesn't exist
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME}`);
    
    console.log(`✅ Database '${process.env.DB_NAME}' created or already exists`);
    
    // Use the database
    await connection.query(`USE ${process.env.DB_NAME}`);
    
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Failed to create database:', error);
    return false;
  }
}

async function runMigration() {
  try {
    console.log('🔄 Running database migration...');
    
    // Path to migration file
    const migrationPath = path.join(process.cwd(), '..', 'supabase', 'migrations', '20250710060353_old_dream.sql');
    
    // Read migration file
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split SQL by semicolons to execute each statement separately
    // This is a simple approach and might not work for all SQL files
    const statements = migrationSQL
      .replace(/--.*$/gm, '') // Remove comments
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    // Execute each statement
    for (const statement of statements) {
      try {
        await executeQuery(statement);
      } catch (error) {
        console.error(`❌ Error executing statement: ${statement.substring(0, 100)}...`);
        console.error(error);
        // Continue with next statement
      }
    }
    
    console.log('✅ Migration completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Migration failed:', error);
    return false;
  }
}

async function seedDefaultData() {
  try {
    console.log('🔄 Checking if default data needs to be seeded...');
    
    // Check if users table is empty
    const usersResult = await executeQuery('SELECT COUNT(*) as count FROM users');
    
    if (usersResult[0].count === 0) {
      console.log('🔄 Seeding default admin user...');
      
      // Create default admin user
      const adminUser = {
        email: '<EMAIL>',
        password: 'password123', // This would be hashed in a real implementation
        first_name: 'Admin',
        last_name: 'User',
        role: 'landlord'
      };
      
      // In a real implementation, you would use your UserModel here
      // For now, we'll just insert directly
      await executeQuery(`
        INSERT INTO users (
          id, email, password_hash, first_name, last_name, role, is_active, email_verified
        ) VALUES (
          UUID(), ?, ?, ?, ?, ?, TRUE, TRUE
        )
      `, [
        adminUser.email,
        adminUser.password, // Should be hashed
        adminUser.first_name,
        adminUser.last_name,
        adminUser.role
      ]);
      
      console.log('✅ Default admin user created');
    } else {
      console.log('✅ Users table already has data, skipping seed');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting database migration process...');
  
  try {
    // Create database if it doesn't exist
    const dbCreated = await createDatabase();
    if (!dbCreated) {
      console.error('❌ Failed to create database, aborting migration');
      process.exit(1);
    }
    
    // Run migration
    const migrationSuccess = await runMigration();
    if (!migrationSuccess) {
      console.error('❌ Migration failed, but will attempt to continue');
    }
    
    // Seed default data
    const seedSuccess = await seedDefaultData();
    if (!seedSuccess) {
      console.error('❌ Seeding failed');
    }
    
    console.log('✅ Database setup completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ An unexpected error occurred:', error);
    process.exit(1);
  } finally {
    // Close database connection
    try {
      await pool.end();
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

// Run the migration
main();
