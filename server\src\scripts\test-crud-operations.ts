import { PropertyModel } from '../models/Property.js';
import { TenantModel } from '../models/Tenant.js';
import { LeaseModel } from '../models/Lease.js';
import { InvoiceModel } from '../models/Invoice.js';
import { PaymentModel } from '../models/Payment.js';
import { ReportModel } from '../models/Report.js';
import { UserModel } from '../models/User.js';
import { UnitModel } from '../models/Unit.js';

async function testCRUDOperations() {
  console.log('🚀 Starting CRUD Operations Test...\n');

  try {
    // Test 1: Properties CRUD
    console.log('📋 Testing Properties CRUD Operations...');
    
    // CREATE Property
    const propertyData = {
      name: 'Test Property CRUD',
      description: 'A test property for CRUD operations',
      address: '123 Test Street',
      city: 'Nairobi',
      state: 'Nairobi County',
      postal_code: '00100',
      country: 'Kenya',
      property_type: 'residential' as const,
      amenities: ['parking', 'security', 'water']
    };

    // Find a landlord user for testing
    const landlords = await UserModel.findByRole('landlord');
    if (landlords.length === 0) {
      throw new Error('No landlord users found for testing');
    }
    const landlordId = landlords[0].id;

    const createdProperty = await PropertyModel.create(propertyData, landlordId);
    console.log('✅ Property Created:', createdProperty.name);

    // READ Property
    const foundProperty = await PropertyModel.findById(createdProperty.id);
    console.log('✅ Property Read:', foundProperty?.name);

    // UPDATE Property
    const updatedProperty = await PropertyModel.update(createdProperty.id, {
      name: 'Updated Test Property CRUD',
      description: 'Updated description for CRUD test'
    });
    console.log('✅ Property Updated:', updatedProperty?.name);

    // Test 2: Units CRUD (needed for other tests)
    console.log('\n📋 Testing Units CRUD Operations...');
    
    const unitData = {
      property_id: createdProperty.id,
      unit_number: 'CRUD-001',
      unit_type: '2br' as const,
      floor_number: 1,
      square_feet: 800,
      bedrooms: 2,
      bathrooms: 1,
      monthly_rent: 25000,
      security_deposit: 50000,
      description: 'Test unit for CRUD operations'
    };

    const createdUnit = await UnitModel.create(unitData);
    console.log('✅ Unit Created:', createdUnit.unit_number);

    // Test 3: Tenants CRUD
    console.log('\n📋 Testing Tenants CRUD Operations...');

    // Find a tenant user for testing
    const tenantUsers = await UserModel.findByRole('tenant');
    if (tenantUsers.length === 0) {
      throw new Error('No tenant users found for testing');
    }
    const tenantUserId = tenantUsers[0].id;

    const tenantData = {
      user_id: tenantUserId,
      emergency_contact_name: 'John Emergency',
      emergency_contact_phone: '+254700000001',
      emergency_contact_relationship: 'Brother',
      employer: 'Test Company Ltd',
      monthly_income: 80000,
      id_number: 'CRUD123456789',
      background_check_status: 'approved' as const,
      move_in_date: new Date('2024-01-01'),
      notes: 'Test tenant for CRUD operations'
    };

    const createdTenant = await TenantModel.create(tenantData);
    console.log('✅ Tenant Created:', createdTenant.id);

    // READ Tenant
    const foundTenant = await TenantModel.findById(createdTenant.id);
    console.log('✅ Tenant Read:', foundTenant?.id);

    // UPDATE Tenant
    const updatedTenant = await TenantModel.update(createdTenant.id, {
      employer: 'Updated Test Company Ltd',
      monthly_income: 90000
    });
    console.log('✅ Tenant Updated:', updatedTenant?.employer);

    // Test 4: Leases CRUD
    console.log('\n📋 Testing Leases CRUD Operations...');

    const leaseData = {
      unit_id: createdUnit.id,
      tenant_id: createdTenant.id,
      lease_type: 'fixed' as const,
      start_date: new Date('2024-01-01'),
      end_date: new Date('2024-12-31'),
      monthly_rent: 25000,
      security_deposit: 50000,
      late_fee_amount: 2500,
      late_fee_type: 'flat' as const,
      grace_period_days: 5,
      status: 'active' as const,
      auto_renew: false,
      renewal_notice_days: 30,
      special_terms: 'Test lease for CRUD operations'
    };

    const createdLease = await LeaseModel.create(leaseData);
    console.log('✅ Lease Created:', createdLease.id);

    // READ Lease
    const foundLease = await LeaseModel.findById(createdLease.id);
    console.log('✅ Lease Read:', foundLease?.id);

    // UPDATE Lease
    const updatedLease = await LeaseModel.update(createdLease.id, {
      monthly_rent: 27000,
      special_terms: 'Updated lease terms for CRUD test'
    });
    console.log('✅ Lease Updated:', updatedLease?.monthly_rent);

    // Test 5: Invoices CRUD
    console.log('\n📋 Testing Invoices CRUD Operations...');

    const invoiceData = {
      lease_id: createdLease.id,
      tenant_id: createdTenant.id,
      invoice_type: 'rent' as const,
      amount: 25000,
      tax_amount: 0,
      due_date: new Date('2024-02-01'),
      issue_date: new Date('2024-01-01'),
      description: 'Monthly rent for January 2024',
      status: 'sent' as const,
      line_items: [
        {
          description: 'Monthly Rent',
          quantity: 1,
          unit_price: 25000,
          total: 25000
        }
      ]
    };

    const createdInvoice = await InvoiceModel.create(invoiceData);
    console.log('✅ Invoice Created:', createdInvoice.invoice_number);

    // READ Invoice
    const foundInvoice = await InvoiceModel.findById(createdInvoice.id);
    console.log('✅ Invoice Read:', foundInvoice?.invoice_number);

    // UPDATE Invoice
    const updatedInvoice = await InvoiceModel.update(createdInvoice.id, {
      status: 'viewed' as const,
      description: 'Updated monthly rent for January 2024'
    });
    console.log('✅ Invoice Updated:', updatedInvoice?.status);

    // Test 6: Payments CRUD
    console.log('\n📋 Testing Payments CRUD Operations...');

    const paymentData = {
      invoice_id: createdInvoice.id,
      tenant_id: createdTenant.id,
      amount: 25000,
      payment_method: 'mpesa' as const,
      payment_gateway: 'safaricom',
      gateway_transaction_id: 'MPESA123456789',
      payment_date: new Date(),
      status: 'completed' as const,
      reference_number: 'REF123456789',
      notes: 'Test payment for CRUD operations',
      fee_amount: 50
    };

    const createdPayment = await PaymentModel.create(paymentData);
    console.log('✅ Payment Created:', createdPayment.transaction_id);

    // READ Payment
    const foundPayment = await PaymentModel.findById(createdPayment.id);
    console.log('✅ Payment Read:', foundPayment?.transaction_id);

    // UPDATE Payment
    const updatedPayment = await PaymentModel.update(createdPayment.id, {
      notes: 'Updated payment notes for CRUD test'
    });
    console.log('✅ Payment Updated:', updatedPayment?.notes);

    // Test 7: Reports READ Operations
    console.log('\n📋 Testing Reports READ Operations...');

    const financialReport = await ReportModel.getFinancialReport(landlordId);
    console.log('✅ Financial Report Generated:', Object.keys(financialReport).length, 'metrics');

    const propertyReport = await ReportModel.getPropertyPerformanceReport(landlordId);
    console.log('✅ Property Performance Report Generated:', propertyReport.length, 'properties');

    const occupancyReport = await ReportModel.getOccupancyReport(landlordId);
    console.log('✅ Occupancy Report Generated:', occupancyReport.length, 'months');

    // Test 8: List Operations with Pagination
    console.log('\n📋 Testing List Operations with Pagination...');

    const propertiesList = await PropertyModel.findAll({ 
      page: 1, 
      limit: 5, 
      landlordId: landlordId 
    });
    console.log('✅ Properties List:', propertiesList.properties.length, 'of', propertiesList.total);

    const tenantsList = await TenantModel.findAll({ 
      page: 1, 
      limit: 5, 
      landlordId: landlordId 
    });
    console.log('✅ Tenants List:', tenantsList.tenants.length, 'of', tenantsList.total);

    const leasesList = await LeaseModel.findAll({ 
      page: 1, 
      limit: 5, 
      landlordId: landlordId 
    });
    console.log('✅ Leases List:', leasesList.leases.length, 'of', leasesList.total);

    const invoicesList = await InvoiceModel.findAll({ 
      page: 1, 
      limit: 5, 
      landlordId: landlordId 
    });
    console.log('✅ Invoices List:', invoicesList.invoices.length, 'of', invoicesList.total);

    const paymentsList = await PaymentModel.findAll({ 
      page: 1, 
      limit: 5, 
      landlordId: landlordId 
    });
    console.log('✅ Payments List:', paymentsList.payments.length, 'of', paymentsList.total);

    // Test 9: DELETE Operations (in reverse order to maintain referential integrity)
    console.log('\n📋 Testing DELETE Operations...');

    // Note: We don't delete payments as they are historical records
    console.log('⚠️  Payments are not deleted (historical records)');

    // Note: We don't delete invoices as they are historical records
    console.log('⚠️  Invoices are not deleted (historical records)');

    // Terminate lease instead of deleting
    const terminatedLease = await LeaseModel.terminate(createdLease.id, 'Test termination for CRUD operations');
    console.log('✅ Lease Terminated:', terminatedLease?.status);

    // Delete tenant (soft delete)
    const deletedTenant = await TenantModel.delete(createdTenant.id);
    console.log('✅ Tenant Deleted (soft):', deletedTenant);

    // Delete unit
    const deletedUnit = await UnitModel.delete(createdUnit.id);
    console.log('✅ Unit Deleted:', deletedUnit);

    // Delete property (soft delete)
    const deletedProperty = await PropertyModel.delete(createdProperty.id);
    console.log('✅ Property Deleted (soft):', deletedProperty);

    console.log('\n🎉 All CRUD Operations Tests Completed Successfully!');
    console.log('\n📊 Summary:');
    console.log('✅ Properties: CREATE, READ, UPDATE, DELETE (soft)');
    console.log('✅ Units: CREATE, READ, UPDATE, DELETE');
    console.log('✅ Tenants: CREATE, READ, UPDATE, DELETE (soft)');
    console.log('✅ Leases: CREATE, READ, UPDATE, TERMINATE');
    console.log('✅ Invoices: CREATE, READ, UPDATE (no delete - historical)');
    console.log('✅ Payments: CREATE, READ, UPDATE (no delete - historical)');
    console.log('✅ Reports: READ operations (financial, property, occupancy)');
    console.log('✅ Pagination and filtering working for all entities');

  } catch (error) {
    console.error('❌ CRUD Operations Test Failed:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testCRUDOperations()
    .then(() => {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

export { testCRUDOperations };
