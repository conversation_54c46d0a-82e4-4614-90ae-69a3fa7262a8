import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Users,
  Building,
  Calendar,
  Download,
  Filter,
  FileText,
  PieChart,
  Activity,
  Target
} from 'lucide-react';
import { reportsAPI } from '../../services/api';

interface ReportData {
  financial: any;
  occupancy: any;
  properties: any[];
  tenants: any[];
}

const ReportsPage: React.FC = () => {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedReport, setSelectedReport] = useState('financial');

  useEffect(() => {
    fetchReportData();
  }, [selectedPeriod]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Mock data for now (would come from API)
      const mockData = {
        financial: {
          total_revenue: 2500000,
          outstanding_amount: 150000,
          actual_monthly_revenue: 800000,
          collection_rate: 94
        },
        occupancy: {
          total_units: 50,
          occupied_units: 47,
          vacant_units: 3,
          current_occupancy_rate: 94
        },
        properties: [
          { id: 1, name: 'Sunset Apartments', total_units: 20, occupancy_rate: 95, actual_monthly_revenue: 400000 },
          { id: 2, name: 'Downtown Office', total_units: 15, occupancy_rate: 87, actual_monthly_revenue: 300000 },
          { id: 3, name: 'Garden View', total_units: 15, occupancy_rate: 100, actual_monthly_revenue: 350000 }
        ],
        tenants: []
      };
      
      setReportData(mockData);
      
    } catch (error) {
      console.error('Failed to fetch report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async (type: string, format: string) => {
    try {
      console.log('Export initiated:', type, format);
      // Would call actual export API
    } catch (error) {
      console.error('Failed to export report:', error);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const ReportCard = ({ title, value, change, icon: Icon, color }: any) => (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <p className={`text-sm mt-1 ${change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
              {change}
            </p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600 mt-1">Comprehensive insights into your property portfolio</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
          <button 
            onClick={() => exportReport(selectedReport, 'pdf')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Download size={16} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <ReportCard
          title="Total Revenue"
          value={`KES ${(reportData?.financial?.total_revenue || 0).toLocaleString()}`}
          change="+12.5% from last month"
          icon={DollarSign}
          color="bg-green-500"
        />
        <ReportCard
          title="Occupancy Rate"
          value={`${reportData?.occupancy?.current_occupancy_rate || 0}%`}
          change="+3.2% from last month"
          icon={Building}
          color="bg-blue-500"
        />
        <ReportCard
          title="Active Tenants"
          value={reportData?.occupancy?.occupied_units || 0}
          change="+5 new tenants"
          icon={Users}
          color="bg-purple-500"
        />
        <ReportCard
          title="Collection Rate"
          value={`${reportData?.financial?.collection_rate || 0}%`}
          change="+2.1% from last month"
          icon={Target}
          color="bg-orange-500"
        />
      </div>

      {/* Report Navigation */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex flex-wrap gap-4 mb-6">
          {[
            { id: 'financial', label: 'Financial', icon: DollarSign },
            { id: 'occupancy', label: 'Occupancy', icon: Building },
            { id: 'properties', label: 'Properties', icon: BarChart3 },
            { id: 'tenants', label: 'Tenants', icon: Users },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedReport(tab.id)}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                selectedReport === tab.id
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <tab.icon size={16} className="mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Report Content */}
        {selectedReport === 'financial' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Financial Performance</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Revenue</span>
                  <span className="font-semibold">KES {(reportData?.financial?.total_revenue || 0).toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Outstanding Amount</span>
                  <span className="font-semibold text-red-600">KES {(reportData?.financial?.outstanding_amount || 0).toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Monthly Revenue</span>
                  <span className="font-semibold">KES {(reportData?.financial?.actual_monthly_revenue || 0).toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Collection Rate</span>
                  <span className="font-semibold text-green-600">{reportData?.financial?.collection_rate || 0}%</span>
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Revenue Breakdown</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Rent Payments</span>
                    <span>85%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Late Fees</span>
                    <span>8%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Other Charges</span>
                    <span>7%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'occupancy' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Occupancy Analysis</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-3xl font-bold text-blue-600">{reportData?.occupancy?.total_units || 0}</p>
                <p className="text-gray-600">Total Units</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-green-600">{reportData?.occupancy?.occupied_units || 0}</p>
                <p className="text-gray-600">Occupied Units</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-red-600">{reportData?.occupancy?.vacant_units || 0}</p>
                <p className="text-gray-600">Vacant Units</p>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="w-full bg-gray-200 rounded-full h-4">
                <div 
                  className="bg-green-500 h-4 rounded-full" 
                  style={{ width: `${reportData?.occupancy?.current_occupancy_rate || 0}%` }}
                ></div>
              </div>
              <p className="text-center mt-2 text-sm text-gray-600">
                {reportData?.occupancy?.current_occupancy_rate || 0}% Occupancy Rate
              </p>
            </div>
          </div>
        )}

        {selectedReport === 'properties' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Property Performance</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Property</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Units</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Occupancy</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Revenue</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {reportData?.properties?.slice(0, 5).map((property: any) => (
                    <tr key={property.id}>
                      <td className="py-3 px-4 font-medium text-gray-900">{property.name}</td>
                      <td className="py-3 px-4 text-gray-600">{property.total_units}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          property.occupancy_rate >= 90 ? 'bg-green-100 text-green-800' :
                          property.occupancy_rate >= 70 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {property.occupancy_rate}%
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        KES {(property.actual_monthly_revenue || 0).toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {selectedReport === 'tenants' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Tenant Analysis</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Payment Performance</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">On-time Payments</span>
                    <span className="font-semibold text-green-600">85%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Late Payments</span>
                    <span className="font-semibold text-yellow-600">12%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Overdue</span>
                    <span className="font-semibold text-red-600">3%</span>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Tenant Retention</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average Tenure</span>
                    <span className="font-semibold">18 months</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Renewal Rate</span>
                    <span className="font-semibold text-green-600">78%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Turnover Rate</span>
                    <span className="font-semibold text-orange-600">22%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Report Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button 
            onClick={() => exportReport('financial', 'pdf')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center"
          >
            <FileText size={20} className="text-blue-600 mx-auto mb-2" />
            <p className="font-medium text-gray-900">Financial Report</p>
          </button>
          <button 
            onClick={() => exportReport('occupancy', 'pdf')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center"
          >
            <PieChart size={20} className="text-green-600 mx-auto mb-2" />
            <p className="font-medium text-gray-900">Occupancy Report</p>
          </button>
          <button 
            onClick={() => exportReport('properties', 'csv')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center"
          >
            <BarChart3 size={20} className="text-purple-600 mx-auto mb-2" />
            <p className="font-medium text-gray-900">Property Data</p>
          </button>
          <button 
            onClick={() => exportReport('tenants', 'csv')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center"
          >
            <Activity size={20} className="text-orange-600 mx-auto mb-2" />
            <p className="font-medium text-gray-900">Tenant Data</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;
