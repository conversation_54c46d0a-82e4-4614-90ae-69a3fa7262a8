import { executeQuery, executeTransaction } from '../config/database';
import { MaintenanceRequest, WorkOrder } from '../types';

export class MaintenanceService {
  // Create maintenance request
  static async createMaintenanceRequest(requestData: {
    unitId: string;
    tenantId?: string;
    reportedBy: string;
    title: string;
    description: string;
    category: string;
    priority: string;
    isEmergency?: boolean;
    requiresTenantAccess?: boolean;
    preferredContactMethod?: string;
    images?: string[];
  }): Promise<string> {
    const requestNumber = await this.generateRequestNumber();
    
    const query = `
      INSERT INTO maintenance_requests (request_number, unit_id, tenant_id, reported_by,
                                       title, description, category, priority, is_emergency,
                                       requires_tenant_access, preferred_contact_method, images)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      requestNumber,
      requestData.unitId,
      requestData.tenantId || null,
      requestData.reportedBy,
      requestData.title,
      requestData.description,
      requestData.category,
      requestData.priority,
      requestData.isEmergency || false,
      requestData.requiresTenantAccess !== false,
      requestData.preferredContactMethod || 'phone',
      JSON.stringify(requestData.images || [])
    ];
    
    const result = await executeQuery(query, params);
    return result.insertId;
  }

  // Get maintenance requests by property
  static async getMaintenanceRequestsByProperty(propertyId: string, status?: string): Promise<MaintenanceRequest[]> {
    let query = `
      SELECT mr.*, 
             u.unit_number,
             p.name as property_name,
             CONCAT(reporter.first_name, ' ', reporter.last_name) as reported_by_name,
             CONCAT(tenant_user.first_name, ' ', tenant_user.last_name) as tenant_name,
             tenant_user.phone as tenant_phone,
             CONCAT(assigned_user.first_name, ' ', assigned_user.last_name) as assigned_to_name
      FROM maintenance_requests mr
      JOIN units u ON mr.unit_id = u.id
      JOIN properties p ON u.property_id = p.id
      JOIN users reporter ON mr.reported_by = reporter.id
      LEFT JOIN tenants t ON mr.tenant_id = t.id
      LEFT JOIN users tenant_user ON t.user_id = tenant_user.id
      LEFT JOIN users assigned_user ON mr.assigned_to = assigned_user.id
      WHERE p.id = ?
    `;
    
    const params = [propertyId];
    
    if (status) {
      query += ' AND mr.status = ?';
      params.push(status);
    }
    
    query += ' ORDER BY mr.created_at DESC';
    
    const results = await executeQuery(query, params);
    return results.map(this.mapDatabaseToMaintenanceRequest);
  }

  // Get maintenance requests by tenant
  static async getMaintenanceRequestsByTenant(tenantId: string): Promise<MaintenanceRequest[]> {
    const query = `
      SELECT mr.*, 
             u.unit_number,
             p.name as property_name,
             CONCAT(assigned_user.first_name, ' ', assigned_user.last_name) as assigned_to_name,
             assigned_user.phone as assigned_to_phone
      FROM maintenance_requests mr
      JOIN units u ON mr.unit_id = u.id
      JOIN properties p ON u.property_id = p.id
      LEFT JOIN users assigned_user ON mr.assigned_to = assigned_user.id
      WHERE mr.tenant_id = ?
      ORDER BY mr.created_at DESC
    `;
    
    const results = await executeQuery(query, [tenantId]);
    return results.map(this.mapDatabaseToMaintenanceRequest);
  }

  // Update maintenance request status
  static async updateMaintenanceRequestStatus(
    requestId: string, 
    status: string, 
    updatedBy: string,
    workNotes?: string,
    actualCost?: number
  ): Promise<void> {
    const updates: string[] = ['status = ?', 'updated_at = NOW()'];
    const params: any[] = [status];
    
    if (workNotes) {
      updates.push('work_notes = ?');
      params.push(workNotes);
    }
    
    if (actualCost !== undefined) {
      updates.push('actual_cost = ?');
      params.push(actualCost);
    }
    
    if (status === 'completed') {
      updates.push('completed_date = NOW()');
    }
    
    params.push(requestId);
    
    const query = `UPDATE maintenance_requests SET ${updates.join(', ')} WHERE id = ?`;
    await executeQuery(query, params);
    
    // Log the status change
    await this.logMaintenanceActivity(requestId, updatedBy, `Status changed to ${status}`, workNotes);
  }

  // Assign maintenance request
  static async assignMaintenanceRequest(
    requestId: string, 
    assignedTo: string, 
    assignedBy: string,
    estimatedCost?: number,
    scheduledDate?: Date
  ): Promise<void> {
    const updates: string[] = ['assigned_to = ?', 'status = ?', 'updated_at = NOW()'];
    const params: any[] = [assignedTo, 'assigned'];
    
    if (estimatedCost !== undefined) {
      updates.push('estimated_cost = ?');
      params.push(estimatedCost);
    }
    
    if (scheduledDate) {
      updates.push('scheduled_date = ?');
      params.push(scheduledDate);
    }
    
    params.push(requestId);
    
    const query = `UPDATE maintenance_requests SET ${updates.join(', ')} WHERE id = ?`;
    await executeQuery(query, params);
    
    // Log the assignment
    await this.logMaintenanceActivity(requestId, assignedBy, `Assigned to user ${assignedTo}`);
  }

  // Create work order
  static async createWorkOrder(workOrderData: {
    maintenanceRequestId: string;
    assignedTo: string;
    contractorName?: string;
    contractorPhone?: string;
    contractorEmail?: string;
    scheduledStart?: Date;
    scheduledEnd?: Date;
    materialsNeeded?: string;
    workDescription?: string;
  }): Promise<string> {
    const workOrderNumber = await this.generateWorkOrderNumber();
    
    const query = `
      INSERT INTO work_orders (work_order_number, maintenance_request_id, assigned_to,
                              contractor_name, contractor_phone, contractor_email,
                              scheduled_start, scheduled_end, materials_needed, work_description)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      workOrderNumber,
      workOrderData.maintenanceRequestId,
      workOrderData.assignedTo,
      workOrderData.contractorName || null,
      workOrderData.contractorPhone || null,
      workOrderData.contractorEmail || null,
      workOrderData.scheduledStart || null,
      workOrderData.scheduledEnd || null,
      workOrderData.materialsNeeded || null,
      workOrderData.workDescription || null
    ];
    
    const result = await executeQuery(query, params);
    
    // Update maintenance request status
    await executeQuery(
      'UPDATE maintenance_requests SET status = ? WHERE id = ?',
      ['assigned', workOrderData.maintenanceRequestId]
    );
    
    return result.insertId;
  }

  // Get maintenance statistics
  static async getMaintenanceStats(propertyId?: string, startDate?: Date, endDate?: Date): Promise<{
    totalRequests: number;
    pendingRequests: number;
    completedRequests: number;
    averageCompletionTime: number;
    totalCost: number;
    categoryBreakdown: { [key: string]: number };
    priorityBreakdown: { [key: string]: number };
  }> {
    let baseQuery = `
      FROM maintenance_requests mr
      JOIN units u ON mr.unit_id = u.id
    `;
    
    const conditions: string[] = [];
    const params: any[] = [];
    
    if (propertyId) {
      conditions.push('u.property_id = ?');
      params.push(propertyId);
    }
    
    if (startDate) {
      conditions.push('mr.created_at >= ?');
      params.push(startDate);
    }
    
    if (endDate) {
      conditions.push('mr.created_at <= ?');
      params.push(endDate);
    }
    
    const whereClause = conditions.length > 0 ? ' WHERE ' + conditions.join(' AND ') : '';
    
    // Get overall stats
    const statsQuery = `
      SELECT 
        COUNT(*) as total_requests,
        COUNT(CASE WHEN mr.status IN ('submitted', 'acknowledged', 'assigned', 'in_progress') THEN 1 END) as pending_requests,
        COUNT(CASE WHEN mr.status = 'completed' THEN 1 END) as completed_requests,
        AVG(CASE WHEN mr.status = 'completed' AND mr.completed_date IS NOT NULL 
            THEN DATEDIFF(mr.completed_date, mr.created_at) END) as avg_completion_days,
        SUM(COALESCE(mr.actual_cost, 0)) as total_cost
      ${baseQuery}${whereClause}
    `;
    
    const [stats] = await executeQuery(statsQuery, params);
    
    // Get category breakdown
    const categoryQuery = `
      SELECT category, COUNT(*) as count
      ${baseQuery}${whereClause}
      GROUP BY category
    `;
    
    const categoryResults = await executeQuery(categoryQuery, params);
    const categoryBreakdown: { [key: string]: number } = {};
    categoryResults.forEach((row: any) => {
      categoryBreakdown[row.category] = row.count;
    });
    
    // Get priority breakdown
    const priorityQuery = `
      SELECT priority, COUNT(*) as count
      ${baseQuery}${whereClause}
      GROUP BY priority
    `;
    
    const priorityResults = await executeQuery(priorityQuery, params);
    const priorityBreakdown: { [key: string]: number } = {};
    priorityResults.forEach((row: any) => {
      priorityBreakdown[row.priority] = row.count;
    });
    
    return {
      totalRequests: stats.total_requests || 0,
      pendingRequests: stats.pending_requests || 0,
      completedRequests: stats.completed_requests || 0,
      averageCompletionTime: parseFloat(stats.avg_completion_days || '0'),
      totalCost: parseFloat(stats.total_cost || '0'),
      categoryBreakdown,
      priorityBreakdown
    };
  }

  // Log maintenance activity
  private static async logMaintenanceActivity(
    requestId: string,
    userId: string,
    action: string,
    notes?: string
  ): Promise<void> {
    const query = `
      INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values)
      VALUES (?, ?, 'maintenance_requests', ?, ?)
    `;
    
    const logData = { action, notes };
    await executeQuery(query, [userId, action, requestId, JSON.stringify(logData)]);
  }

  // Generate unique request number
  private static async generateRequestNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    const query = `
      SELECT COUNT(*) as count
      FROM maintenance_requests
      WHERE request_number LIKE ?
    `;
    
    const prefix = `MR-${year}${month}`;
    const [result] = await executeQuery(query, [`${prefix}%`]);
    const sequence = String(result.count + 1).padStart(4, '0');
    
    return `${prefix}-${sequence}`;
  }

  // Generate unique work order number
  private static async generateWorkOrderNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    const query = `
      SELECT COUNT(*) as count
      FROM work_orders
      WHERE work_order_number LIKE ?
    `;
    
    const prefix = `WO-${year}${month}`;
    const [result] = await executeQuery(query, [`${prefix}%`]);
    const sequence = String(result.count + 1).padStart(4, '0');
    
    return `${prefix}-${sequence}`;
  }

  // Map database result to MaintenanceRequest object
  private static mapDatabaseToMaintenanceRequest(row: any): MaintenanceRequest {
    return {
      id: row.id,
      requestNumber: row.request_number,
      unitId: row.unit_id,
      tenantId: row.tenant_id,
      reportedBy: row.reported_by,
      title: row.title,
      description: row.description,
      category: row.category,
      priority: row.priority,
      status: row.status,
      assignedTo: row.assigned_to,
      estimatedCost: row.estimated_cost ? parseFloat(row.estimated_cost) : undefined,
      actualCost: row.actual_cost ? parseFloat(row.actual_cost) : undefined,
      scheduledDate: row.scheduled_date ? new Date(row.scheduled_date) : undefined,
      completedDate: row.completed_date ? new Date(row.completed_date) : undefined,
      images: row.images ? JSON.parse(row.images) : [],
      workNotes: row.work_notes,
      tenantSatisfactionRating: row.tenant_satisfaction_rating,
      tenantFeedback: row.tenant_feedback,
      isEmergency: row.is_emergency,
      requiresTenantAccess: row.requires_tenant_access,
      preferredContactMethod: row.preferred_contact_method,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      // Additional fields from joins
      unitNumber: row.unit_number,
      propertyName: row.property_name,
      reportedByName: row.reported_by_name,
      tenantName: row.tenant_name,
      tenantPhone: row.tenant_phone,
      assignedToName: row.assigned_to_name,
      assignedToPhone: row.assigned_to_phone
    };
  }
}