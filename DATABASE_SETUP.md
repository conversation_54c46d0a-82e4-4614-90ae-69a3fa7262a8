# Database Setup Guide

## Prerequisites
- MySQL Server installed and running
- MySQL Workbench or command line access

## Step 1: Create Database

### Option A: Using MySQL Workbench
1. Open MySQL Workbench
2. Connect to your MySQL server
3. Execute the following SQL:
```sql
CREATE DATABASE IF NOT EXISTS property_management;
USE property_management;
```

### Option B: Using Command Line
```bash
mysql -u root -p
```
Then execute:
```sql
CREATE DATABASE IF NOT EXISTS property_management;
USE property_management;
```

## Step 2: Import Schema

### Option A: Using MySQL Workbench
1. Open the file `supabase/migrations/20250710060353_old_dream.sql`
2. Copy the entire content
3. Paste it into a new SQL tab in MySQL Workbench
4. Execute the script

### Option B: Using Command Line
```bash
mysql -u root -p property_management < supabase/migrations/20250710060353_old_dream.sql
```

## Step 3: Verify Setup

Execute this query to verify tables were created:
```sql
SHOW TABLES;
```

You should see tables like:
- users
- properties
- units
- tenants
- leases
- invoices
- payments
- maintenance_requests
- etc.

## Step 4: Create Test User (Optional)

```sql
INSERT INTO users (
    id, 
    email, 
    password_hash, 
    first_name, 
    last_name, 
    role, 
    is_active, 
    email_verified
) VALUES (
    UUID(),
    '<EMAIL>',
    '$2b$12$LQv3c1yqBwEHFl5ePEJ./.H.DqX9jmjKQbXD8VqKzY8QzY8QzY8Qz', -- password: 'password123'
    'Admin',
    'User',
    'landlord',
    TRUE,
    TRUE
);
```

## Troubleshooting

### Common Issues:

1. **Access Denied Error**
   - Make sure your MySQL user has proper permissions
   - Update the DB_PASSWORD in server/.env

2. **Connection Refused**
   - Ensure MySQL server is running
   - Check DB_HOST and DB_PORT in server/.env

3. **Database Already Exists**
   - This is fine, the script will use the existing database

4. **Table Already Exists Errors**
   - You can safely ignore these if re-running the migration

## Environment Configuration

Make sure your `server/.env` file has the correct database settings:

```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=property_management
```
