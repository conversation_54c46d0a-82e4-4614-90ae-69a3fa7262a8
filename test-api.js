// Simple API test script
const API_BASE = 'http://localhost:5000/api/v1';

async function testAPI() {
  console.log('🚀 Testing PropertyPro API Endpoints...\n');
  
  // Test health endpoint
  try {
    console.log('🔄 Testing health endpoint...');
    const response = await fetch('http://localhost:5000/health');
    const data = await response.json();
    console.log('✅ Health check:', data.status);
    console.log('   Uptime:', Math.round(data.uptime), 'seconds');
    console.log('   Environment:', data.environment);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    console.log('   Make sure the backend server is running on port 5000');
    return;
  }
  
  // Test auth endpoints
  console.log('\n🔄 Testing authentication endpoints...');
  
  // Test registration
  try {
    const registerResponse = await fetch(`${API_BASE}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Test',
        last_name: 'User',
        role: 'landlord'
      })
    });
    
    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      console.log('✅ User registration successful');
      console.log('   User ID:', registerData.data.user.id);
      console.log('   Token received:', registerData.data.token ? 'Yes' : 'No');
      
      // Test login with the same credentials
      console.log('\n🔄 Testing login...');
      const loginResponse = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });
      
      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        console.log('✅ Login successful');
        console.log('   User:', loginData.data.user.first_name, loginData.data.user.last_name);
        console.log('   Role:', loginData.data.user.role);
        
        // Test protected endpoint
        console.log('\n🔄 Testing protected endpoint...');
        const token = loginData.data.token;
        
        const meResponse = await fetch(`${API_BASE}/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (meResponse.ok) {
          const meData = await meResponse.json();
          console.log('✅ Protected endpoint access successful');
          console.log('   Current user:', meData.data.first_name, meData.data.last_name);
        } else {
          console.log('❌ Protected endpoint failed:', meResponse.status);
        }
        
        // Test dashboard stats
        console.log('\n🔄 Testing dashboard stats...');
        const statsResponse = await fetch(`${API_BASE}/dashboard/stats`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          console.log('✅ Dashboard stats retrieved');
          console.log('   Total Properties:', statsData.data.totalProperties);
          console.log('   Total Units:', statsData.data.totalUnits);
          console.log('   Occupancy Rate:', statsData.data.occupancyRate + '%');
        } else {
          console.log('❌ Dashboard stats failed:', statsResponse.status);
        }
        
        // Test properties endpoint
        console.log('\n🔄 Testing properties endpoint...');
        const propertiesResponse = await fetch(`${API_BASE}/properties`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (propertiesResponse.ok) {
          const propertiesData = await propertiesResponse.json();
          console.log('✅ Properties endpoint working');
          console.log('   Properties found:', propertiesData.data.length);
          console.log('   Pagination:', propertiesData.pagination);
        } else {
          console.log('❌ Properties endpoint failed:', propertiesResponse.status);
        }
        
      } else {
        const loginError = await loginResponse.json();
        console.log('❌ Login failed:', loginError.error);
      }
      
    } else {
      const registerError = await registerResponse.json();
      if (registerError.error && registerError.error.includes('already registered')) {
        console.log('ℹ️  User already exists, testing login...');
        
        // Test login with existing user
        const loginResponse = await fetch(`${API_BASE}/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123'
          })
        });
        
        if (loginResponse.ok) {
          const loginData = await loginResponse.json();
          console.log('✅ Login with existing user successful');
          console.log('   User:', loginData.data.user.first_name, loginData.data.user.last_name);
        } else {
          console.log('❌ Login with existing user failed');
        }
      } else {
        console.log('❌ Registration failed:', registerError.error);
      }
    }
    
  } catch (error) {
    console.log('❌ Authentication test failed:', error.message);
  }
  
  console.log('\n🎉 API testing completed!');
  console.log('\n📋 Summary:');
  console.log('   - Backend server is running');
  console.log('   - Database connection is working');
  console.log('   - Authentication endpoints are functional');
  console.log('   - Protected routes are secured');
  console.log('   - Dashboard and properties endpoints are accessible');
}

// Run the test
testAPI().catch(console.error);
