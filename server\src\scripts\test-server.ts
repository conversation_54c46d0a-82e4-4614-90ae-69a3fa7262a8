import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 Testing server configuration...');

// Test environment variables
console.log('\n📋 Environment Variables:');
console.log('NODE_ENV:', process.env.NODE_ENV || 'development');
console.log('PORT:', process.env.PORT || '5000');
console.log('DB_HOST:', process.env.DB_HOST || 'localhost');
console.log('DB_PORT:', process.env.DB_PORT || '3306');
console.log('DB_USER:', process.env.DB_USER || 'root');
console.log('DB_NAME:', process.env.DB_NAME || 'property_management');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '✅ Set' : '❌ Not set');
console.log('JWT_REFRESH_SECRET:', process.env.JWT_REFRESH_SECRET ? '✅ Set' : '❌ Not set');

// Test database connection
async function testDatabaseConnection() {
  try {
    console.log('\n🔄 Testing database connection...');
    
    const { testConnection } = await import('../config/database.js');
    const isConnected = await testConnection();
    
    if (isConnected) {
      console.log('✅ Database connection successful');
      return true;
    } else {
      console.log('❌ Database connection failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Database connection error:', error.message);
    return false;
  }
}

// Test server startup
async function testServerStartup() {
  try {
    console.log('\n🔄 Testing server startup...');
    
    // Import and start server
    const app = await import('../server.js');
    console.log('✅ Server modules loaded successfully');
    return true;
  } catch (error) {
    console.log('❌ Server startup error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 PropertyPro Backend Test Suite\n');
  
  let allTestsPassed = true;
  
  // Test database connection
  const dbConnected = await testDatabaseConnection();
  if (!dbConnected) {
    allTestsPassed = false;
    console.log('\n⚠️  Database connection failed. Please check:');
    console.log('   1. MySQL server is running');
    console.log('   2. Database credentials in .env are correct');
    console.log('   3. Database "property_management" exists');
  }
  
  // Test server startup
  const serverStarted = await testServerStartup();
  if (!serverStarted) {
    allTestsPassed = false;
  }
  
  console.log('\n📊 Test Results:');
  console.log('Database Connection:', dbConnected ? '✅ PASS' : '❌ FAIL');
  console.log('Server Startup:', serverStarted ? '✅ PASS' : '❌ FAIL');
  
  if (allTestsPassed) {
    console.log('\n🎉 All tests passed! Server is ready to start.');
  } else {
    console.log('\n⚠️  Some tests failed. Please fix the issues above.');
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

main().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
