import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

async function seedDatabase() {
  console.log('🌱 Starting database seeding...\n');

  try {
    // 1. Seed Users
    console.log('👥 Seeding users...');
    
    const users = [
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'Admin',
        last_name: 'User',
        role: 'landlord',
        phone: '+************',
        is_active: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'Property',
        last_name: 'Manager',
        role: 'property_manager',
        phone: '+************',
        is_active: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        role: 'tenant',
        phone: '+************',
        is_active: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'Jane',
        last_name: 'Smith',
        role: 'tenant',
        phone: '+************',
        is_active: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'Mike',
        last_name: 'Wilson',
        role: 'caretaker',
        phone: '+************',
        is_active: true
      }
    ];

    for (const user of users) {
      await executeQuery(`
        INSERT IGNORE INTO users (id, email, password_hash, first_name, last_name, role, phone, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [user.id, user.email, user.password, user.first_name, user.last_name, user.role, user.phone, user.is_active]);
    }

    console.log(`✅ Seeded ${users.length} users`);

    // Get user IDs for relationships
    const landlordUser = users.find(u => u.role === 'landlord');
    const managerUser = users.find(u => u.role === 'property_manager');
    const tenant1User = users.find(u => u.email === '<EMAIL>');
    const tenant2User = users.find(u => u.email === '<EMAIL>');
    const caretakerUser = users.find(u => u.role === 'caretaker');

    // 2. Seed Properties
    console.log('🏢 Seeding properties...');
    
    const properties = [
      {
        id: uuidv4(),
        name: 'Sunset Apartments',
        description: 'Modern apartment complex with great amenities',
        address: '123 Sunset Boulevard',
        city: 'Nairobi',
        state: 'Nairobi County',
        postal_code: '00100',
        country: 'Kenya',
        property_type: 'residential',
        landlord_id: landlordUser?.id,
        manager_id: managerUser?.id,
        images: JSON.stringify(['sunset1.jpg', 'sunset2.jpg']),
        amenities: JSON.stringify(['parking', 'security', 'water', 'electricity'])
      },
      {
        id: uuidv4(),
        name: 'Downtown Office Complex',
        description: 'Prime office space in the heart of the city',
        address: '456 Business District',
        city: 'Nairobi',
        state: 'Nairobi County',
        postal_code: '00200',
        country: 'Kenya',
        property_type: 'commercial',
        landlord_id: landlordUser?.id,
        manager_id: managerUser?.id,
        images: JSON.stringify(['office1.jpg', 'office2.jpg']),
        amenities: JSON.stringify(['parking', 'security', 'elevator', 'conference_room'])
      },
      {
        id: uuidv4(),
        name: 'Garden View Residences',
        description: 'Peaceful residential complex with garden views',
        address: '789 Garden Lane',
        city: 'Nairobi',
        state: 'Nairobi County',
        postal_code: '00300',
        country: 'Kenya',
        property_type: 'residential',
        landlord_id: landlordUser?.id,
        manager_id: managerUser?.id,
        images: JSON.stringify(['garden1.jpg', 'garden2.jpg']),
        amenities: JSON.stringify(['garden', 'parking', 'security', 'playground'])
      }
    ];

    for (const property of properties) {
      await executeQuery(`
        INSERT IGNORE INTO properties (id, name, description, address, city, state, postal_code, country, property_type, landlord_id, manager_id, images, amenities)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [property.id, property.name, property.description, property.address, property.city, property.state, property.postal_code, property.country, property.property_type, property.landlord_id, property.manager_id, property.images, property.amenities]);
    }

    console.log(`✅ Seeded ${properties.length} properties`);

    // 3. Seed Units
    console.log('🏠 Seeding units...');
    
    const units = [
      // Sunset Apartments units
      {
        id: uuidv4(),
        property_id: properties[0].id,
        unit_number: 'A101',
        unit_type: '2br',
        floor_number: 1,
        square_feet: 850,
        bedrooms: 2,
        bathrooms: 1,
        monthly_rent: 25000,
        security_deposit: 50000,
        is_occupied: true,
        description: 'Spacious 2-bedroom apartment with balcony'
      },
      {
        id: uuidv4(),
        property_id: properties[0].id,
        unit_number: 'A102',
        unit_type: '1br',
        floor_number: 1,
        square_feet: 600,
        bedrooms: 1,
        bathrooms: 1,
        monthly_rent: 18000,
        security_deposit: 36000,
        is_occupied: false,
        description: 'Cozy 1-bedroom apartment'
      },
      {
        id: uuidv4(),
        property_id: properties[0].id,
        unit_number: 'A201',
        unit_type: '3br',
        floor_number: 2,
        square_feet: 1200,
        bedrooms: 3,
        bathrooms: 2,
        monthly_rent: 35000,
        security_deposit: 70000,
        is_occupied: true,
        description: 'Large 3-bedroom family apartment'
      },
      // Downtown Office units
      {
        id: uuidv4(),
        property_id: properties[1].id,
        unit_number: 'Suite 101',
        unit_type: 'office',
        floor_number: 1,
        square_feet: 500,
        bedrooms: 0,
        bathrooms: 1,
        monthly_rent: 40000,
        security_deposit: 80000,
        is_occupied: false,
        description: 'Modern office suite with city view'
      },
      // Garden View units
      {
        id: uuidv4(),
        property_id: properties[2].id,
        unit_number: 'G101',
        unit_type: '2br',
        floor_number: 1,
        square_feet: 900,
        bedrooms: 2,
        bathrooms: 2,
        monthly_rent: 28000,
        security_deposit: 56000,
        is_occupied: false,
        description: '2-bedroom apartment with garden view'
      }
    ];

    for (const unit of units) {
      await executeQuery(`
        INSERT IGNORE INTO units (id, property_id, unit_number, unit_type, floor_number, square_feet, bedrooms, bathrooms, monthly_rent, security_deposit, is_occupied, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [unit.id, unit.property_id, unit.unit_number, unit.unit_type, unit.floor_number, unit.square_feet, unit.bedrooms, unit.bathrooms, unit.monthly_rent, unit.security_deposit, unit.is_occupied, unit.description]);
    }

    console.log(`✅ Seeded ${units.length} units`);

    // 4. Seed Tenants
    console.log('👤 Seeding tenants...');
    
    const tenants = [
      {
        id: uuidv4(),
        user_id: tenant1User?.id,
        emergency_contact_name: 'Mary Doe',
        emergency_contact_phone: '+************',
        emergency_contact_relationship: 'Sister',
        employer: 'Tech Solutions Ltd',
        monthly_income: 80000,
        id_number: '12345678',
        background_check_status: 'approved',
        move_in_date: new Date('2024-01-15'),
        notes: 'Excellent tenant, always pays on time'
      },
      {
        id: uuidv4(),
        user_id: tenant2User?.id,
        emergency_contact_name: 'Robert Smith',
        emergency_contact_phone: '+************',
        emergency_contact_relationship: 'Father',
        employer: 'Marketing Agency',
        monthly_income: 75000,
        id_number: '87654321',
        background_check_status: 'approved',
        move_in_date: new Date('2024-02-01'),
        notes: 'Professional tenant, good references'
      }
    ];

    for (const tenant of tenants) {
      await executeQuery(`
        INSERT IGNORE INTO tenants (id, user_id, emergency_contact_name, emergency_contact_phone, emergency_contact_relationship, employer, monthly_income, id_number, background_check_status, move_in_date, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [tenant.id, tenant.user_id, tenant.emergency_contact_name, tenant.emergency_contact_phone, tenant.emergency_contact_relationship, tenant.employer, tenant.monthly_income, tenant.id_number, tenant.background_check_status, tenant.move_in_date, tenant.notes]);
    }

    console.log(`✅ Seeded ${tenants.length} tenants`);

    // 5. Seed Leases
    console.log('📋 Seeding leases...');
    
    const occupiedUnits = units.filter(u => u.is_occupied);
    const leases = [
      {
        id: uuidv4(),
        unit_id: occupiedUnits[0].id,
        tenant_id: tenants[0].id,
        lease_type: 'fixed',
        start_date: new Date('2024-01-15'),
        end_date: new Date('2024-12-31'),
        monthly_rent: occupiedUnits[0].monthly_rent,
        security_deposit: occupiedUnits[0].security_deposit,
        late_fee_amount: 2500,
        late_fee_type: 'flat',
        grace_period_days: 5,
        status: 'active',
        auto_renew: false,
        renewal_notice_days: 30,
        special_terms: 'No pets allowed'
      },
      {
        id: uuidv4(),
        unit_id: occupiedUnits[1].id,
        tenant_id: tenants[1].id,
        lease_type: 'fixed',
        start_date: new Date('2024-02-01'),
        end_date: new Date('2025-01-31'),
        monthly_rent: occupiedUnits[1].monthly_rent,
        security_deposit: occupiedUnits[1].security_deposit,
        late_fee_amount: 3000,
        late_fee_type: 'flat',
        grace_period_days: 5,
        status: 'active',
        auto_renew: true,
        renewal_notice_days: 60,
        special_terms: 'Parking space included'
      }
    ];

    for (const lease of leases) {
      await executeQuery(`
        INSERT IGNORE INTO leases (id, unit_id, tenant_id, lease_type, start_date, end_date, monthly_rent, security_deposit, late_fee_amount, late_fee_type, grace_period_days, status, auto_renew, renewal_notice_days, special_terms)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [lease.id, lease.unit_id, lease.tenant_id, lease.lease_type, lease.start_date, lease.end_date, lease.monthly_rent, lease.security_deposit, lease.late_fee_amount, lease.late_fee_type, lease.grace_period_days, lease.status, lease.auto_renew, lease.renewal_notice_days, lease.special_terms]);
    }

    console.log(`✅ Seeded ${leases.length} leases`);

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   👥 Users: ${users.length}`);
    console.log(`   🏢 Properties: ${properties.length}`);
    console.log(`   🏠 Units: ${units.length}`);
    console.log(`   👤 Tenants: ${tenants.length}`);
    console.log(`   📋 Leases: ${leases.length}`);
    
    console.log('\n🔑 Login Credentials:');
    console.log('   Landlord: <EMAIL> / password123');
    console.log('   Manager: <EMAIL> / password123');
    console.log('   Tenant 1: <EMAIL> / password123');
    console.log('   Tenant 2: <EMAIL> / password123');
    console.log('   Caretaker: <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  }
}

// Run the seeding if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase()
    .then(() => {
      console.log('\n✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Seeding failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
