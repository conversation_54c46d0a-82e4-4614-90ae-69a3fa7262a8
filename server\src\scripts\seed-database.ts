import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

async function seedDatabase() {
  console.log('🌱 Starting comprehensive database seeding...\n');

  try {
    // Clear existing data (in reverse order of dependencies)
    console.log('🧹 Clearing existing data...');
    await executeQuery('DELETE FROM payments');
    await executeQuery('DELETE FROM invoices');
    await executeQuery('DELETE FROM leases');
    await executeQuery('DELETE FROM tenants');
    await executeQuery('DELETE FROM units');
    await executeQuery('DELETE FROM properties');
    await executeQuery('DELETE FROM users WHERE email LIKE "%@propertypro.com"');
    console.log('✅ Cleared existing data\n');

    // 1. Seed Users
    console.log('👥 Seeding users...');

    const users = [
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'Admin',
        last_name: 'User',
        role: 'admin',
        phone: '+************',
        is_active: true,
        email_verified: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'James',
        last_name: 'Landlord',
        role: 'landlord',
        phone: '+************',
        is_active: true,
        email_verified: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'Sarah',
        last_name: 'Manager',
        role: 'property_manager',
        phone: '+************',
        is_active: true,
        email_verified: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'John',
        last_name: 'Doe',
        role: 'tenant',
        phone: '+************',
        is_active: true,
        email_verified: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'Jane',
        last_name: 'Smith',
        role: 'tenant',
        phone: '+************',
        is_active: true,
        email_verified: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'Michael',
        last_name: 'Johnson',
        role: 'tenant',
        phone: '+************',
        is_active: true,
        email_verified: true
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        first_name: 'David',
        last_name: 'Wilson',
        role: 'caretaker',
        phone: '+************',
        is_active: true,
        email_verified: true
      }
    ];

    for (const user of users) {
      await executeQuery(`
        INSERT INTO users (id, email, password_hash, first_name, last_name, role, phone, is_active, email_verified)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [user.id, user.email, user.password, user.first_name, user.last_name, user.role, user.phone, user.is_active, user.email_verified]);
    }

    console.log(`✅ Seeded ${users.length} users`);

    // Get user IDs for relationships
    const adminUser = users.find(u => u.role === 'admin');
    const landlordUser = users.find(u => u.role === 'landlord');
    const managerUser = users.find(u => u.role === 'property_manager');
    const tenant1User = users.find(u => u.email === '<EMAIL>');
    const tenant2User = users.find(u => u.email === '<EMAIL>');
    const tenant3User = users.find(u => u.email === '<EMAIL>');
    const caretakerUser = users.find(u => u.role === 'caretaker');

    // 2. Seed Properties
    console.log('🏢 Seeding properties...');

    const properties = [
      {
        id: uuidv4(),
        name: 'Sunset Apartments',
        description: 'Modern apartment complex with great amenities including swimming pool, gym, and 24/7 security',
        address: '123 Sunset Boulevard',
        city: 'Nairobi',
        state: 'Nairobi County',
        postal_code: '00100',
        country: 'Kenya',
        property_type: 'residential',
        landlord_id: landlordUser?.id,
        manager_id: managerUser?.id,
        images: JSON.stringify(['sunset1.jpg', 'sunset2.jpg', 'sunset3.jpg']),
        amenities: JSON.stringify(['parking', 'security', 'water', 'electricity', 'swimming_pool', 'gym', 'elevator']),
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Downtown Office Complex',
        description: 'Prime office space in the heart of the city with modern facilities and excellent connectivity',
        address: '456 Business District',
        city: 'Nairobi',
        state: 'Nairobi County',
        postal_code: '00200',
        country: 'Kenya',
        property_type: 'commercial',
        landlord_id: landlordUser?.id,
        manager_id: managerUser?.id,
        images: JSON.stringify(['office1.jpg', 'office2.jpg', 'office3.jpg']),
        amenities: JSON.stringify(['parking', 'security', 'elevator', 'conference_room', 'high_speed_internet', 'air_conditioning']),
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Garden View Residences',
        description: 'Peaceful residential complex with beautiful garden views and family-friendly amenities',
        address: '789 Garden Lane',
        city: 'Nairobi',
        state: 'Nairobi County',
        postal_code: '00300',
        country: 'Kenya',
        property_type: 'residential',
        landlord_id: landlordUser?.id,
        manager_id: managerUser?.id,
        images: JSON.stringify(['garden1.jpg', 'garden2.jpg', 'garden3.jpg']),
        amenities: JSON.stringify(['garden', 'parking', 'security', 'playground', 'water', 'electricity', 'backup_generator']),
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Westlands Mixed Development',
        description: 'Mixed-use development with residential and commercial spaces in prime Westlands location',
        address: '321 Westlands Avenue',
        city: 'Nairobi',
        state: 'Nairobi County',
        postal_code: '00400',
        country: 'Kenya',
        property_type: 'mixed',
        landlord_id: landlordUser?.id,
        manager_id: managerUser?.id,
        images: JSON.stringify(['westlands1.jpg', 'westlands2.jpg']),
        amenities: JSON.stringify(['parking', 'security', 'elevator', 'shopping_center', 'restaurant', 'water', 'electricity']),
        is_active: true
      }
    ];

    for (const property of properties) {
      await executeQuery(`
        INSERT INTO properties (id, name, description, address, city, state, postal_code, country, property_type, landlord_id, manager_id, images, amenities, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [property.id, property.name, property.description, property.address, property.city, property.state, property.postal_code, property.country, property.property_type, property.landlord_id, property.manager_id, property.images, property.amenities, property.is_active]);
    }

    console.log(`✅ Seeded ${properties.length} properties`);

    // 3. Seed Units
    console.log('🏠 Seeding units...');

    const units = [
      // Sunset Apartments units (Property 1)
      {
        id: uuidv4(),
        property_id: properties[0].id,
        unit_number: 'A101',
        unit_type: '2br',
        floor_number: 1,
        square_feet: 850,
        bedrooms: 2,
        bathrooms: 1,
        monthly_rent: 25000,
        security_deposit: 50000,
        is_occupied: true,
        is_available: true,
        description: 'Spacious 2-bedroom apartment with balcony and city view',
        amenities: JSON.stringify(['balcony', 'air_conditioning', 'fitted_kitchen'])
      },
      {
        id: uuidv4(),
        property_id: properties[0].id,
        unit_number: 'A102',
        unit_type: '1br',
        floor_number: 1,
        square_feet: 600,
        bedrooms: 1,
        bathrooms: 1,
        monthly_rent: 18000,
        security_deposit: 36000,
        is_occupied: false,
        is_available: true,
        description: 'Cozy 1-bedroom apartment perfect for singles',
        amenities: JSON.stringify(['fitted_kitchen', 'wardrobe'])
      },
      {
        id: uuidv4(),
        property_id: properties[0].id,
        unit_number: 'A201',
        unit_type: '3br',
        floor_number: 2,
        square_feet: 1200,
        bedrooms: 3,
        bathrooms: 2,
        monthly_rent: 35000,
        security_deposit: 70000,
        is_occupied: true,
        is_available: true,
        description: 'Large 3-bedroom family apartment with master ensuite',
        amenities: JSON.stringify(['balcony', 'master_ensuite', 'fitted_kitchen', 'family_room'])
      },
      {
        id: uuidv4(),
        property_id: properties[0].id,
        unit_number: 'A202',
        unit_type: '2br',
        floor_number: 2,
        square_feet: 850,
        bedrooms: 2,
        bathrooms: 1,
        monthly_rent: 26000,
        security_deposit: 52000,
        is_occupied: false,
        is_available: true,
        description: 'Well-lit 2-bedroom apartment on second floor',
        amenities: JSON.stringify(['balcony', 'fitted_kitchen'])
      },
      // Downtown Office units (Property 2)
      {
        id: uuidv4(),
        property_id: properties[1].id,
        unit_number: 'Suite 101',
        unit_type: 'office',
        floor_number: 1,
        square_feet: 500,
        bedrooms: 0,
        bathrooms: 1,
        monthly_rent: 40000,
        security_deposit: 80000,
        is_occupied: false,
        is_available: true,
        description: 'Modern office suite with city view and reception area',
        amenities: JSON.stringify(['reception_area', 'conference_room', 'high_speed_internet'])
      },
      {
        id: uuidv4(),
        property_id: properties[1].id,
        unit_number: 'Suite 201',
        unit_type: 'office',
        floor_number: 2,
        square_feet: 750,
        bedrooms: 0,
        bathrooms: 2,
        monthly_rent: 55000,
        security_deposit: 110000,
        is_occupied: true,
        is_available: true,
        description: 'Large office suite with multiple rooms and executive facilities',
        amenities: JSON.stringify(['executive_office', 'meeting_rooms', 'kitchenette', 'high_speed_internet'])
      },
      // Garden View units (Property 3)
      {
        id: uuidv4(),
        property_id: properties[2].id,
        unit_number: 'G101',
        unit_type: '2br',
        floor_number: 1,
        square_feet: 900,
        bedrooms: 2,
        bathrooms: 2,
        monthly_rent: 28000,
        security_deposit: 56000,
        is_occupied: false,
        is_available: true,
        description: '2-bedroom apartment with beautiful garden view',
        amenities: JSON.stringify(['garden_view', 'fitted_kitchen', 'walk_in_closet'])
      },
      {
        id: uuidv4(),
        property_id: properties[2].id,
        unit_number: 'G102',
        unit_type: '3br',
        floor_number: 1,
        square_feet: 1100,
        bedrooms: 3,
        bathrooms: 2,
        monthly_rent: 32000,
        security_deposit: 64000,
        is_occupied: true,
        is_available: true,
        description: 'Spacious 3-bedroom ground floor apartment with garden access',
        amenities: JSON.stringify(['garden_access', 'master_ensuite', 'fitted_kitchen', 'storage_room'])
      },
      // Westlands Mixed Development units (Property 4)
      {
        id: uuidv4(),
        property_id: properties[3].id,
        unit_number: 'W101',
        unit_type: '1br',
        floor_number: 1,
        square_feet: 650,
        bedrooms: 1,
        bathrooms: 1,
        monthly_rent: 22000,
        security_deposit: 44000,
        is_occupied: false,
        is_available: true,
        description: 'Modern 1-bedroom apartment in mixed development',
        amenities: JSON.stringify(['fitted_kitchen', 'balcony', 'shopping_access'])
      },
      {
        id: uuidv4(),
        property_id: properties[3].id,
        unit_number: 'Shop 01',
        unit_type: 'retail',
        floor_number: 0,
        square_feet: 400,
        bedrooms: 0,
        bathrooms: 1,
        monthly_rent: 45000,
        security_deposit: 90000,
        is_occupied: false,
        is_available: true,
        description: 'Ground floor retail space with street frontage',
        amenities: JSON.stringify(['street_frontage', 'display_windows', 'storage_area'])
      }
    ];

    for (const unit of units) {
      await executeQuery(`
        INSERT INTO units (id, property_id, unit_number, unit_type, floor_number, square_feet, bedrooms, bathrooms, monthly_rent, security_deposit, is_occupied, is_available, description, amenities)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [unit.id, unit.property_id, unit.unit_number, unit.unit_type, unit.floor_number, unit.square_feet, unit.bedrooms, unit.bathrooms, unit.monthly_rent, unit.security_deposit, unit.is_occupied, unit.is_available, unit.description, unit.amenities]);
    }

    console.log(`✅ Seeded ${units.length} units`);

    // 4. Seed Tenants
    console.log('👤 Seeding tenants...');

    const tenants = [
      {
        id: uuidv4(),
        user_id: tenant1User?.id,
        emergency_contact_name: 'Mary Doe',
        emergency_contact_phone: '+************',
        emergency_contact_relationship: 'Sister',
        employer: 'Tech Solutions Ltd',
        monthly_income: 80000,
        id_number: '12345678',
        background_check_status: 'approved',
        background_check_date: new Date('2024-01-10'),
        move_in_date: new Date('2024-01-15'),
        notes: 'Excellent tenant, always pays on time. Software engineer with stable income.'
      },
      {
        id: uuidv4(),
        user_id: tenant2User?.id,
        emergency_contact_name: 'Robert Smith',
        emergency_contact_phone: '+************',
        emergency_contact_relationship: 'Father',
        employer: 'Marketing Agency Kenya',
        monthly_income: 75000,
        id_number: '87654321',
        background_check_status: 'approved',
        background_check_date: new Date('2024-01-25'),
        move_in_date: new Date('2024-02-01'),
        notes: 'Professional tenant, good references. Marketing manager with 5 years experience.'
      },
      {
        id: uuidv4(),
        user_id: tenant3User?.id,
        emergency_contact_name: 'Sarah Johnson',
        emergency_contact_phone: '+************',
        emergency_contact_relationship: 'Wife',
        employer: 'Nairobi Financial Services',
        monthly_income: 95000,
        id_number: '11223344',
        background_check_status: 'approved',
        background_check_date: new Date('2024-02-10'),
        move_in_date: new Date('2024-02-15'),
        notes: 'Senior financial analyst, excellent credit history. Family man with stable employment.'
      }
    ];

    for (const tenant of tenants) {
      await executeQuery(`
        INSERT INTO tenants (id, user_id, emergency_contact_name, emergency_contact_phone, emergency_contact_relationship, employer, monthly_income, id_number, background_check_status, background_check_date, move_in_date, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [tenant.id, tenant.user_id, tenant.emergency_contact_name, tenant.emergency_contact_phone, tenant.emergency_contact_relationship, tenant.employer, tenant.monthly_income, tenant.id_number, tenant.background_check_status, tenant.background_check_date, tenant.move_in_date, tenant.notes]);
    }

    console.log(`✅ Seeded ${tenants.length} tenants`);

    // 5. Seed Leases
    console.log('📋 Seeding leases...');

    const occupiedUnits = units.filter(u => u.is_occupied);
    const leases = [
      {
        id: uuidv4(),
        unit_id: occupiedUnits[0].id, // A101 - 2br apartment
        tenant_id: tenants[0].id, // John Doe
        lease_type: 'fixed',
        start_date: new Date('2024-01-15'),
        end_date: new Date('2024-12-31'),
        monthly_rent: occupiedUnits[0].monthly_rent,
        security_deposit: occupiedUnits[0].security_deposit,
        late_fee_amount: 2500,
        late_fee_type: 'flat',
        grace_period_days: 5,
        status: 'active',
        auto_renew: false,
        renewal_notice_days: 30,
        special_terms: 'No pets allowed. Tenant responsible for utilities.'
      },
      {
        id: uuidv4(),
        unit_id: occupiedUnits[1].id, // A201 - 3br apartment
        tenant_id: tenants[1].id, // Jane Smith
        lease_type: 'fixed',
        start_date: new Date('2024-02-01'),
        end_date: new Date('2025-01-31'),
        monthly_rent: occupiedUnits[1].monthly_rent,
        security_deposit: occupiedUnits[1].security_deposit,
        late_fee_amount: 3000,
        late_fee_type: 'flat',
        grace_period_days: 5,
        status: 'active',
        auto_renew: true,
        renewal_notice_days: 60,
        special_terms: 'Parking space included. Water and garbage collection included in rent.'
      },
      {
        id: uuidv4(),
        unit_id: occupiedUnits[2].id, // Suite 201 - Office
        tenant_id: tenants[2].id, // Michael Johnson
        lease_type: 'fixed',
        start_date: new Date('2024-02-15'),
        end_date: new Date('2025-02-14'),
        monthly_rent: occupiedUnits[2].monthly_rent,
        security_deposit: occupiedUnits[2].security_deposit,
        late_fee_amount: 5000,
        late_fee_type: 'flat',
        grace_period_days: 3,
        status: 'active',
        auto_renew: false,
        renewal_notice_days: 90,
        special_terms: 'Commercial lease. Tenant responsible for all utilities and maintenance. 24/7 access included.'
      },
      {
        id: uuidv4(),
        unit_id: occupiedUnits[3].id, // G102 - 3br garden apartment
        tenant_id: tenants[0].id, // John Doe (second lease for demo)
        lease_type: 'month_to_month',
        start_date: new Date('2024-03-01'),
        end_date: new Date('2024-12-31'),
        monthly_rent: occupiedUnits[3].monthly_rent,
        security_deposit: occupiedUnits[3].security_deposit,
        late_fee_amount: 2800,
        late_fee_type: 'flat',
        grace_period_days: 5,
        status: 'draft',
        auto_renew: true,
        renewal_notice_days: 30,
        special_terms: 'Month-to-month lease. Garden maintenance included. Pet-friendly unit.'
      }
    ];

    for (const lease of leases) {
      await executeQuery(`
        INSERT INTO leases (id, unit_id, tenant_id, lease_type, start_date, end_date, monthly_rent, security_deposit, late_fee_amount, late_fee_type, grace_period_days, status, auto_renew, renewal_notice_days, special_terms)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [lease.id, lease.unit_id, lease.tenant_id, lease.lease_type, lease.start_date, lease.end_date, lease.monthly_rent, lease.security_deposit, lease.late_fee_amount, lease.late_fee_type, lease.grace_period_days, lease.status, lease.auto_renew, lease.renewal_notice_days, lease.special_terms]);
    }

    console.log(`✅ Seeded ${leases.length} leases`);

    // 6. Seed Invoices
    console.log('🧾 Seeding invoices...');

    const activeLeases = leases.filter(l => l.status === 'active');
    const invoices = [];

    // Generate invoices for active leases
    for (let i = 0; i < activeLeases.length; i++) {
      const lease = activeLeases[i];

      // Generate 3 months of invoices for each lease
      for (let month = 0; month < 3; month++) {
        const issueDate = new Date();
        issueDate.setMonth(issueDate.getMonth() - (2 - month));
        issueDate.setDate(1);

        const dueDate = new Date(issueDate);
        dueDate.setDate(5);

        const invoice = {
          id: uuidv4(),
          invoice_number: `INV-${issueDate.getFullYear()}${String(issueDate.getMonth() + 1).padStart(2, '0')}-${String(i + 1).padStart(3, '0')}${String(month + 1).padStart(2, '0')}`,
          lease_id: lease.id,
          tenant_id: lease.tenant_id,
          invoice_type: 'rent',
          amount: lease.monthly_rent,
          tax_amount: 0,
          total_amount: lease.monthly_rent,
          due_date: dueDate,
          issue_date: issueDate,
          description: `Monthly rent for ${issueDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`,
          status: month < 2 ? 'paid' : 'sent',
          late_fee_applied: false,
          discount_amount: 0,
          payment_terms: 'Net 5',
          notes: null
        };

        invoices.push(invoice);
      }
    }

    for (const invoice of invoices) {
      await executeQuery(`
        INSERT INTO invoices (id, invoice_number, lease_id, tenant_id, invoice_type, amount, tax_amount, total_amount, due_date, issue_date, description, status, late_fee_applied, discount_amount, payment_terms, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [invoice.id, invoice.invoice_number, invoice.lease_id, invoice.tenant_id, invoice.invoice_type, invoice.amount, invoice.tax_amount, invoice.total_amount, invoice.due_date, invoice.issue_date, invoice.description, invoice.status, invoice.late_fee_applied, invoice.discount_amount, invoice.payment_terms, invoice.notes]);
    }

    console.log(`✅ Seeded ${invoices.length} invoices`);

    // 7. Seed Payments
    console.log('💳 Seeding payments...');

    const paidInvoices = invoices.filter(i => i.status === 'paid');
    const payments = [];

    for (let i = 0; i < paidInvoices.length; i++) {
      const invoice = paidInvoices[i];

      const payment = {
        id: uuidv4(),
        transaction_id: `TXN-${Date.now()}-${String(i + 1).padStart(4, '0')}`,
        invoice_id: invoice.id,
        tenant_id: invoice.tenant_id,
        amount: invoice.total_amount,
        payment_method: ['mpesa', 'bank_transfer', 'card'][Math.floor(Math.random() * 3)],
        payment_gateway: 'mpesa',
        gateway_transaction_id: `MP${Date.now()}${i}`,
        payment_date: new Date(invoice.due_date.getTime() - Math.random() * 5 * 24 * 60 * 60 * 1000), // Random date before due date
        status: 'completed',
        reference_number: `REF${Date.now()}${i}`,
        notes: 'Payment processed successfully',
        fee_amount: Math.floor(invoice.total_amount * 0.01) // 1% fee
      };

      payments.push(payment);
    }

    for (const payment of payments) {
      await executeQuery(`
        INSERT INTO payments (id, transaction_id, invoice_id, tenant_id, amount, payment_method, payment_gateway, gateway_transaction_id, payment_date, status, reference_number, notes, fee_amount)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [payment.id, payment.transaction_id, payment.invoice_id, payment.tenant_id, payment.amount, payment.payment_method, payment.payment_gateway, payment.gateway_transaction_id, payment.payment_date, payment.status, payment.reference_number, payment.notes, payment.fee_amount]);
    }

    console.log(`✅ Seeded ${payments.length} payments`);

    console.log('\n🎉 Comprehensive database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   👥 Users: ${users.length}`);
    console.log(`   🏢 Properties: ${properties.length}`);
    console.log(`   🏠 Units: ${units.length}`);
    console.log(`   👤 Tenants: ${tenants.length}`);
    console.log(`   📋 Leases: ${leases.length}`);
    console.log(`   🧾 Invoices: ${invoices.length}`);
    console.log(`   💳 Payments: ${payments.length}`);

    console.log('\n🔑 Login Credentials:');
    console.log('   Admin: <EMAIL> / password123');
    console.log('   Landlord: <EMAIL> / password123');
    console.log('   Manager: <EMAIL> / password123');
    console.log('   Tenant 1: <EMAIL> / password123');
    console.log('   Tenant 2: <EMAIL> / password123');
    console.log('   Tenant 3: <EMAIL> / password123');
    console.log('   Caretaker: <EMAIL> / password123');

    console.log('\n🏗️ Database Structure:');
    console.log('   ✅ All tables seeded with realistic data');
    console.log('   ✅ Proper relationships established');
    console.log('   ✅ Active leases with current invoices');
    console.log('   ✅ Payment history for completed invoices');
    console.log('   ✅ Mixed property types (residential, commercial, mixed)');
    console.log('   ✅ Various unit types and occupancy statuses');

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  }
}

// Run the seeding if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase()
    .then(() => {
      console.log('\n✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Seeding failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
