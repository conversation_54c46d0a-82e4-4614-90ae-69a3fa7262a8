// User types
export interface User {
  id: string;
  email: string;
  password_hash?: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: 'admin' | 'landlord' | 'property_manager' | 'caretaker' | 'tenant';
  avatar_url?: string;
  is_active: boolean;
  email_verified: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: 'admin' | 'landlord' | 'property_manager' | 'caretaker' | 'tenant';
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// Pagination types
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

// Request types
export interface CreatePropertyRequest {
  name: string;
  description?: string;
  address: string;
  city: string;
  state: string;
  postal_code?: string;
  country: string;
  property_type: 'residential' | 'commercial' | 'mixed';
  manager_id?: string;
  images?: string[];
  amenities?: string[];
}

export interface CreateUnitRequest {
  property_id: string;
  unit_number: string;
  unit_type: 'studio' | '1br' | '2br' | '3br' | '4br' | 'office' | 'retail' | 'warehouse';
  floor_number?: number;
  square_feet?: number;
  bedrooms: number;
  bathrooms: number;
  monthly_rent: number;
  security_deposit: number;
  description?: string;
  amenities?: string[];
  images?: string[];
}

// Property types
export interface Property {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state: string;
  postal_code?: string;
  country: string;
  property_type: 'residential' | 'commercial' | 'mixed';
  landlord_id: string;
  manager_id?: string;
  images?: string[];
  amenities?: string[];
  is_active: boolean;
  total_units?: number;
  occupied_units?: number;
  created_at: Date;
  updated_at: Date;
}

// Unit types
export interface Unit {
  id: string;
  property_id: string;
  unit_number: string;
  unit_type: 'studio' | '1br' | '2br' | '3br' | '4br' | 'office' | 'retail' | 'warehouse';
  floor_number?: number;
  square_feet?: number;
  bedrooms: number;
  bathrooms: number;
  monthly_rent: number;
  security_deposit: number;
  is_occupied: boolean;
  description?: string;
  amenities?: string[];
  images?: string[];
  created_at: Date;
  updated_at: Date;
}

// Tenant types
export interface Tenant {
  id: string;
  user_id: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  employer?: string;
  monthly_income?: number;
  id_number?: string;
  id_document_url?: string;
  background_check_status: 'pending' | 'approved' | 'rejected';
  move_in_date?: Date;
  move_out_date?: Date;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

// Lease types
export interface Lease {
  id: string;
  unit_id: string;
  tenant_id: string;
  lease_type: 'fixed' | 'month_to_month' | 'periodic';
  start_date: Date;
  end_date: Date;
  monthly_rent: number;
  security_deposit: number;
  late_fee_amount: number;
  late_fee_type: 'flat' | 'percentage';
  grace_period_days: number;
  lease_document_url?: string;
  status: 'draft' | 'active' | 'expired' | 'terminated';
  termination_date?: Date;
  termination_reason?: string;
  auto_renew: boolean;
  renewal_notice_days: number;
  special_terms?: string;
  created_at: Date;
  updated_at: Date;
}

// Invoice types
export interface InvoiceLineItem {
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  lease_id?: string;
  tenant_id: string;
  invoice_type: 'rent' | 'utilities' | 'maintenance' | 'late_fee' | 'other';
  amount: number;
  tax_amount: number;
  total_amount: number;
  due_date: Date;
  issue_date: Date;
  description?: string;
  line_items?: InvoiceLineItem[];
  status: 'draft' | 'sent' | 'viewed' | 'paid' | 'overdue' | 'cancelled';
  late_fee_applied: boolean;
  discount_amount: number;
  payment_terms: string;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

// Payment types
export interface Payment {
  id: string;
  transaction_id: string;
  invoice_id?: string;
  tenant_id: string;
  amount: number;
  payment_method: 'mpesa' | 'bank_transfer' | 'card' | 'cash' | 'check';
  payment_gateway?: string;
  gateway_transaction_id?: string;
  gateway_response?: any;
  payment_date: Date;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  reference_number?: string;
  notes?: string;
  fee_amount: number;
  created_at: Date;
  updated_at: Date;
}

// Maintenance types
export interface MaintenanceRequest {
  id: string;
  unit_id: string;
  tenant_id?: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'plumbing' | 'electrical' | 'hvac' | 'appliance' | 'structural' | 'other';
  status: 'submitted' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
  assigned_to?: string;
  estimated_cost?: number;
  actual_cost?: number;
  scheduled_date?: Date;
  completed_date?: Date;
  images?: string[];
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

// Notification types
export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  is_read: boolean;
  action_url?: string;
  created_at: Date;
  updated_at: Date;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Dashboard types
export interface DashboardStats {
  totalProperties: number;
  totalUnits: number;
  occupiedUnits: number;
  vacantUnits: number;
  occupancyRate: number;
  totalTenants: number;
  activeTenants: number;
  monthlyRevenue: number;
  actualRevenue: number;
  outstandingAmount: number;
  maintenanceRequests: number;
  urgentMaintenance: number;
  pendingInvoices: number;
  overdueInvoices: number;
  totalInvoiceAmount: number;
  completedMaintenance: number;
}

// Report types
export interface FinancialReport {
  totalRevenue: number;
  monthlyRevenue: number;
  outstandingAmount: number;
  collectionRate: number;
  totalExpenses: number;
  netIncome: number;
  revenueByMonth: Array<{
    month: string;
    revenue: number;
    expenses: number;
    netIncome: number;
  }>;
}

export interface OccupancyReport {
  totalUnits: number;
  occupiedUnits: number;
  vacantUnits: number;
  occupancyRate: number;
  averageRent: number;
  occupancyByProperty: Array<{
    propertyName: string;
    totalUnits: number;
    occupiedUnits: number;
    occupancyRate: number;
  }>;
}

// Error types
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}
