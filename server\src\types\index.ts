// User types
export interface User {
  id: string;
  email: string;
  password_hash?: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: 'landlord' | 'property_manager' | 'caretaker' | 'tenant';
  avatar_url?: string;
  is_active: boolean;
  email_verified: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: 'landlord' | 'property_manager' | 'caretaker' | 'tenant';
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: Omit<User, 'password_hash'>;
  token: string;
  refreshToken: string;
}

// Property types
export interface Property {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state?: string;
  postal_code?: string;
  country: string;
  property_type: 'residential' | 'commercial' | 'mixed';
  total_units: number;
  landlord_id: string;
  manager_id?: string;
  images?: string[];
  amenities?: string[];
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CreatePropertyRequest {
  name: string;
  description?: string;
  address: string;
  city: string;
  state?: string;
  postal_code?: string;
  country?: string;
  property_type: 'residential' | 'commercial' | 'mixed';
  manager_id?: string;
  images?: string[];
  amenities?: string[];
}

// Unit types
export interface Unit {
  id: string;
  property_id: string;
  unit_number: string;
  unit_type: 'studio' | '1br' | '2br' | '3br' | '4br' | 'office' | 'retail' | 'warehouse';
  floor_number?: number;
  square_feet?: number;
  bedrooms: number;
  bathrooms: number;
  monthly_rent: number;
  security_deposit: number;
  description?: string;
  amenities?: string[];
  images?: string[];
  is_occupied: boolean;
  is_available: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUnitRequest {
  property_id: string;
  unit_number: string;
  unit_type: 'studio' | '1br' | '2br' | '3br' | '4br' | 'office' | 'retail' | 'warehouse';
  floor_number?: number;
  square_feet?: number;
  bedrooms?: number;
  bathrooms?: number;
  monthly_rent: number;
  security_deposit: number;
  description?: string;
  amenities?: string[];
  images?: string[];
}

// Tenant types
export interface Tenant {
  id: string;
  user_id: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  employer?: string;
  monthly_income?: number;
  id_number?: string;
  id_document_url?: string;
  background_check_status: 'pending' | 'approved' | 'rejected';
  background_check_date?: Date;
  move_in_date?: Date;
  move_out_date?: Date;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

// Lease types
export interface Lease {
  id: string;
  unit_id: string;
  tenant_id: string;
  lease_type: 'fixed' | 'month_to_month' | 'commercial';
  start_date: Date;
  end_date?: Date;
  monthly_rent: number;
  security_deposit: number;
  late_fee_amount: number;
  late_fee_type: 'flat' | 'percentage';
  grace_period_days: number;
  lease_document_url?: string;
  status: 'draft' | 'active' | 'expired' | 'terminated' | 'renewed';
  termination_date?: Date;
  termination_reason?: string;
  auto_renew: boolean;
  renewal_notice_days: number;
  special_terms?: string;
  created_at: Date;
  updated_at: Date;
}

// Invoice types
export interface InvoiceLineItem {
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  lease_id: string;
  tenant_id: string;
  invoice_type: 'rent' | 'deposit' | 'late_fee' | 'utility' | 'maintenance' | 'other';
  amount: number;
  tax_amount: number;
  total_amount: number;
  due_date: Date;
  issue_date: Date;
  description?: string;
  line_items: InvoiceLineItem[];
  status: 'draft' | 'sent' | 'viewed' | 'paid' | 'partial' | 'overdue' | 'cancelled';
  paid_amount: number;
  payment_date?: Date;
  late_fee_applied: number;
  pdf_url?: string;
  reminder_sent_count: number;
  last_reminder_sent?: Date;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

// Payment types
export interface Payment {
  id: string;
  invoice_id: string;
  tenant_id: string;
  amount: number;
  payment_method: 'mpesa' | 'bank_transfer' | 'card' | 'cash' | 'cheque';
  transaction_reference?: string;
  mpesa_receipt_number?: string;
  payment_date: Date;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  gateway_response?: any;
  reconciliation_status: 'pending' | 'matched' | 'unmatched';
  reconciled_at?: Date;
  reconciled_by?: string;
  fees: number;
  net_amount: number;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

// Maintenance types
export interface MaintenanceRequest {
  id: string;
  request_number: string;
  unit_id: string;
  tenant_id?: string;
  reported_by: string;
  title: string;
  description: string;
  category: 'plumbing' | 'electrical' | 'hvac' | 'appliance' | 'structural' | 'pest_control' | 'cleaning' | 'security' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent' | 'emergency';
  status: 'submitted' | 'acknowledged' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';
  assigned_to?: string;
  estimated_cost?: number;
  actual_cost?: number;
  scheduled_date?: Date;
  completed_date?: Date;
  images?: string[];
  work_notes?: string;
  tenant_satisfaction_rating?: number;
  tenant_feedback?: string;
  is_emergency: boolean;
  requires_tenant_access: boolean;
  preferred_contact_method: 'phone' | 'email' | 'sms';
  created_at: Date;
  updated_at: Date;
}

// Dashboard types
export interface DashboardStats {
  totalProperties: number;
  totalUnits: number;
  occupiedUnits: number;
  vacantUnits: number;
  occupancyRate: number;
  monthlyRevenue: number;
  actualRevenue: number;
  pendingInvoices: number;
  overdueInvoices: number;
  totalInvoiceAmount: number;
  maintenanceRequests: number;
  urgentMaintenance: number;
  completedMaintenance: number;
  totalTenants: number;
  newTenantsThisMonth: number;
  leasesExpiringThisMonth: number;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

// Request types
export interface AuthenticatedRequest extends Request {
  user?: User;
}
