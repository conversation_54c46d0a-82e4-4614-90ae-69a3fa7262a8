# PropertyPro Enterprise - Property Management Solution

A comprehensive web-based enterprise solution for property management and rent collection that supports multi-user access for landlords, property managers, caretakers, and tenants.

## 🎯 Features

### Core Modules
- **User Roles & Access Control**: Support for landlords, property managers, caretakers, and tenants
- **Property & Unit Management**: Register buildings and units with detailed information
- **Lease & Tenant Tracking**: Complete lease management with automated renewals
- **Rent Invoicing & Automated Reminders**: Auto-generate monthly invoices with smart reminders
- **Online Rent Collection**: Integrated M-Pesa, bank transfers, and card payments
- **Maintenance Request Management**: Complete workflow from request to completion
- **Late Payment Alerts & Penalty Engine**: Automated late fee calculations
- **Reporting & Dashboard**: Real-time insights and exportable reports

### Advanced Features
- **Multi-property scalability**: Manage unlimited properties and units
- **Audit trail**: Complete logging of all actions and changes
- **Email/SMS notifications**: Automated communication with tenants
- **Document management**: Store and manage lease documents, invoices, and receipts
- **Payment reconciliation**: Automatic matching of payments to invoices
- **Maintenance work orders**: Detailed work order management with cost tracking

## 🛠 Tech Stack

- **Frontend**: React.js with TypeScript, Tailwind CSS, Vite
- **Backend**: Node.js with Express, TypeScript
- **Database**: MySQL with comprehensive schema
- **Authentication**: JWT-based with role-based access control
- **API**: RESTful API with comprehensive endpoints
- **Integrations**: M-Pesa API, Email/SMS gateways (Africa's Talking)
- **File Storage**: Local storage with cloud options
- **State Management**: React Context API
- **Form Handling**: React Hook Form
- **Data Visualization**: Recharts

## 📋 Database Schema

The system includes 15+ comprehensive database tables:

### Core Tables
- `users` - User management with role-based access
- `properties` - Property information and management
- `units` - Individual unit details and specifications
- `tenants` - Extended tenant information and background checks
- `leases` - Comprehensive lease management
- `invoices` - Detailed invoicing with line items
- `payments` - Payment processing and reconciliation
- `maintenance_requests` - Complete maintenance workflow
- `work_orders` - Detailed work order management

### Supporting Tables
- `property_assignments` - Property manager assignments
- `notifications` - In-app notification system
- `audit_logs` - Complete audit trail
- `system_settings` - Configurable system settings
- `email_templates` - Customizable email templates
- `sms_templates` - SMS notification templates
- `communication_logs` - Communication tracking

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd property-management-system
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Install backend dependencies**
   ```bash
   cd server
   npm install
   cd ..
   ```

4. **Database Setup**
   ```bash
   # Create MySQL database
   mysql -u root -p
   CREATE DATABASE property_management;

   # Import schema
   mysql -u root -p property_management < supabase/migrations/20250710060353_old_dream.sql
   ```

5. **Environment Configuration**
   ```bash
   # Copy environment templates
   cp .env.example .env
   cp server/.env.example server/.env

   # Create frontend local env
   cp .env.local.example .env.local

   # Edit environment files with your configuration
   ```

6. **Start the application**
   ```bash
   # Start backend server (in one terminal)
   cd server
   npm run dev

   # Start frontend development server (in another terminal)
   npm run dev

   # Production mode
   npm run build
   npm start
   ```

### Environment Variables

Key environment variables to configure:

```env
# Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=property_management

# Authentication
JWT_SECRET=your_jwt_secret

# M-Pesa Integration
MPESA_CONSUMER_KEY=your_key
MPESA_CONSUMER_SECRET=your_secret
MPESA_SHORTCODE=your_shortcode

# Email/SMS
SMTP_HOST=smtp.gmail.com
AT_API_KEY=your_africas_talking_key
```

## 👥 User Roles & Permissions

### Landlord
- Full access to all properties they own
- Manage property managers and caretakers
- View all financial reports and analytics
- Configure system settings

### Property Manager
- Manage assigned properties
- Handle tenant relationships and leases
- Process payments and generate invoices
- Coordinate maintenance activities

### Caretaker
- View assigned properties
- Manage maintenance requests
- Update work order status
- Communicate with tenants

### Tenant
- View personal lease information
- Make rent payments online
- Submit maintenance requests
- Access payment history and invoices

## 📊 Key Features Detail

### Invoice Management
- Automatic monthly rent invoice generation
- Customizable invoice templates
- Late fee calculation with configurable rules
- PDF generation and email delivery
- Payment tracking and reconciliation

### Payment Processing
- M-Pesa STK Push integration
- Bank transfer reconciliation
- Card payment processing
- Automatic payment matching
- Comprehensive payment reporting

### Maintenance Management
- Multi-step approval workflow
- Priority-based request handling
- Work order generation and tracking
- Cost estimation and actual cost tracking
- Tenant satisfaction ratings

### Reporting & Analytics
- Real-time dashboard with key metrics
- Occupancy rate tracking
- Revenue analysis and forecasting
- Maintenance cost analysis
- Exportable reports (PDF/Excel)

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Refresh JWT token

### Properties
- `GET /api/properties` - List properties
- `POST /api/properties` - Create property
- `GET /api/properties/:id` - Get property details
- `PUT /api/properties/:id` - Update property
- `DELETE /api/properties/:id` - Delete property

### Units
- `GET /api/properties/:id/units` - List units
- `POST /api/properties/:id/units` - Create unit
- `PUT /api/units/:id` - Update unit
- `DELETE /api/units/:id` - Delete unit

### Invoices
- `GET /api/invoices` - List invoices
- `POST /api/invoices` - Create invoice
- `POST /api/invoices/generate-monthly` - Generate monthly invoices
- `GET /api/invoices/:id/pdf` - Download invoice PDF

### Payments
- `GET /api/payments` - List payments
- `POST /api/payments` - Process payment
- `POST /api/payments/mpesa-callback` - M-Pesa callback
- `POST /api/payments/reconcile` - Reconcile payments

### Maintenance
- `GET /api/maintenance` - List maintenance requests
- `POST /api/maintenance` - Create maintenance request
- `PUT /api/maintenance/:id` - Update maintenance request
- `POST /api/maintenance/:id/work-orders` - Create work order

## 🔒 Security Features

- JWT-based authentication with refresh tokens
- Role-based access control (RBAC)
- Password hashing with bcrypt
- Rate limiting on API endpoints
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration

## 📱 Mobile Responsiveness

The application is fully responsive and works seamlessly on:
- Desktop computers
- Tablets
- Mobile phones (optimized for screens as small as 320px width)
- Progressive Web App (PWA) capabilities

### Mobile-First Design Features
- Responsive navigation with mobile-friendly sidebar
- Adaptive layouts that reorganize content for smaller screens
- Touch-friendly UI elements with appropriate sizing
- Optimized typography and spacing for readability
- Efficient use of screen real estate on all devices

## 🚀 Deployment

### Production Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Configure production environment**
   ```bash
   # Set production environment variables
   export NODE_ENV=production
   export DB_HOST=your_production_db_host
   # ... other production variables
   ```

3. **Deploy to cloud platforms**
   - AWS EC2/RDS
   - DigitalOcean Droplets
   - Google Cloud Platform
   - Heroku
   - Vercel (frontend)

### Docker Deployment

```dockerfile
# Dockerfile included for containerized deployment
docker build -t property-management .
docker run -p 3000:3000 property-management
```

## 📈 Performance Optimization

- Database indexing for optimal query performance
- Connection pooling for database connections
- Caching strategies for frequently accessed data
- Image optimization and lazy loading
- Code splitting and bundle optimization
- CDN integration for static assets

## 🧪 Testing

```bash
# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run end-to-end tests
npm run test:e2e

# Generate coverage report
npm run test:coverage
```

## 📚 Documentation

- API documentation available at `/api/docs`
- Database schema documentation in `/docs/database.md`
- User manual in `/docs/user-guide.md`
- Developer guide in `/docs/developer-guide.md`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [docs.propertypro.com](https://docs.propertypro.com)
- Issues: [GitHub Issues](https://github.com/your-repo/issues)

## 🎯 Roadmap

### Phase 1 (Current)
- ✅ Core property management features
- ✅ Basic invoicing and payments
- ✅ Maintenance request system
- ✅ User role management

### Phase 2 (Next)
- 🔄 Advanced reporting and analytics
- 🔄 Mobile app development
- 🔄 Integration with accounting software
- 🔄 Automated lease renewals

### Phase 3 (Future)
- 📋 AI-powered maintenance predictions
- 📋 Advanced tenant screening
- 📋 IoT device integration
- 📋 Multi-language support

---

**PropertyPro Enterprise** - Streamlining property management for the modern world.