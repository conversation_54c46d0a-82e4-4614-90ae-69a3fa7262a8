# PropertyPro Complete Setup Guide

## 🎯 Overview
This guide will help you set up the PropertyPro Enterprise system with real database integration and remove all mock data.

## ✅ What's Been Updated

### Backend Integration
- ✅ Real API endpoints with MySQL database
- ✅ JWT authentication with secure secrets
- ✅ Properties API with real data
- ✅ Dashboard API with actual statistics
- ✅ User management with role-based access

### Frontend Updates
- ✅ Removed all mock data
- ✅ Connected to real API endpoints
- ✅ Mobile-responsive design
- ✅ Real-time data loading
- ✅ Error handling and loading states

## 🚀 Step-by-Step Setup

### Step 1: Database Setup (XAMPP MySQL)

1. **Start XAMPP**
   - Open XAMPP Control Panel
   - Start Apache and MySQL services

2. **Create Database**
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Create new database: `property_management`

3. **Import Schema and Data**
   - In phpMyAdmin, select the `property_management` database
   - Go to "Import" tab
   - Choose file: `setup-database.sql` (from project root)
   - Click "Go" to execute

   **Alternative: Manual Setup**
   - Copy the content from `setup-database.sql`
   - Paste it in the SQL tab in phpMyAdmin
   - Execute the script

### Step 2: Backend Configuration

1. **Install Dependencies**
   ```bash
   cd server
   npm install
   ```

2. **Environment Setup**
   - Your `server/.env` is already configured with:
     - Database: `property_management`
     - User: `root`
     - Password: (empty for XAMPP)
     - JWT secrets: ✅ Already set

3. **Test Backend**
   ```bash
   npm run test:server
   ```
   Should show: ✅ Database connection successful

### Step 3: Start the Servers

1. **Start Backend** (Terminal 1)
   ```bash
   cd server
   npm run dev
   ```
   Expected output:
   ```
   🚀 PropertyPro Backend Server running on port 5000
   ✅ Database connection established successfully
   ```

2. **Start Frontend** (Terminal 2)
   ```bash
   npm run dev
   ```
   Expected output:
   ```
   Local: http://localhost:5173/
   ```

### Step 4: Test the System

1. **Open Application**
   - Navigate to: http://localhost:5173

2. **Login with Sample Data**
   ```
   Email: <EMAIL>
   Password: password123
   Role: Landlord
   ```

3. **Verify Real Data**
   - Dashboard should show actual statistics
   - Properties page should display 3 sample properties
   - All data comes from MySQL database

## 📊 Sample Data Included

### Users
- **Admin User** (Landlord): <EMAIL>
- **Property Manager**: <EMAIL>  
- **Tenant**: <EMAIL>
- Password for all: `password123`

### Properties
1. **Sunset Apartments** (Residential)
   - 4 units (2 occupied, 2 available)
   - Monthly revenue: KES 75,000
   - Potential: KES 132,000

2. **Downtown Office Complex** (Commercial)
   - 3 offices (1 occupied, 2 available)
   - Monthly revenue: KES 80,000
   - Potential: KES 275,000

3. **Garden View Residences** (Residential)
   - 3 units (1 occupied, 2 available)
   - Monthly revenue: KES 50,000
   - Potential: KES 123,000

## 🧪 API Testing

Test the API endpoints:

```bash
# Health check
curl http://localhost:5000/health

# Login
curl -X POST http://localhost:5000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get properties (use token from login)
curl http://localhost:5000/api/v1/properties \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 📱 Mobile Testing

Test responsive design:
1. Open browser developer tools (F12)
2. Toggle device toolbar
3. Test different screen sizes:
   - iPhone SE (375px)
   - iPad (768px)
   - Desktop (1200px+)

## 🔧 Troubleshooting

### Database Issues
- **Connection failed**: Check XAMPP MySQL is running
- **Database not found**: Ensure `property_management` database exists
- **Import failed**: Check SQL syntax in phpMyAdmin

### Backend Issues
- **Port 5000 in use**: Kill existing processes or change port
- **Module not found**: Run `npm install` in server directory
- **JWT errors**: Check JWT secrets in `server/.env`

### Frontend Issues
- **API calls failing**: Ensure backend is running on port 5000
- **Login not working**: Check browser console for errors
- **Data not loading**: Verify API endpoints are accessible

## ✅ Success Indicators

You'll know everything is working when:

1. **Backend**: 
   ```
   ✅ Database connection established successfully
   🚀 PropertyPro Backend Server running on port 5000
   ```

2. **Frontend**:
   ```
   ✅ Login <NAME_EMAIL>
   ✅ Dashboard shows real statistics (not zeros)
   ✅ Properties page displays 3 sample properties
   ✅ Mobile responsive design works
   ```

3. **Database**:
   ```
   ✅ 3 users in users table
   ✅ 3 properties in properties table  
   ✅ 10 units in units table
   ✅ All foreign key relationships working
   ```

## 🎯 Next Steps

After successful setup:

1. **Add More Data**
   - Create additional properties
   - Add more units
   - Register new users

2. **Test Features**
   - Property management
   - Unit assignments
   - Dashboard analytics
   - Mobile responsiveness

3. **Customize**
   - Update branding
   - Modify property types
   - Adjust currency formatting

## 📞 Support

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify all services are running
3. Ensure database connection is working
4. Test API endpoints individually

---

**PropertyPro Enterprise** - Now with real database integration and mobile-first design! 🚀
