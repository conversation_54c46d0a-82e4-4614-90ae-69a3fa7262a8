import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { AppError } from './errorHandler.js';

// Validation middleware factory
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return next(new AppError(errorMessage, 400));
    }
    
    next();
  };
};

// Common validation schemas
export const schemas = {
  // User schemas
  createUser: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required(),
    first_name: Joi.string().min(2).max(50).required(),
    last_name: Joi.string().min(2).max(50).required(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    role: Joi.string().valid('landlord', 'property_manager', 'caretaker', 'tenant').required()
  }),
  
  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),
  
  updateUser: Joi.object({
    first_name: Joi.string().min(2).max(50).optional(),
    last_name: Joi.string().min(2).max(50).optional(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    avatar_url: Joi.string().uri().optional()
  }),
  
  // Property schemas
  createProperty: Joi.object({
    name: Joi.string().min(2).max(255).required(),
    description: Joi.string().max(1000).optional(),
    address: Joi.string().min(5).max(500).required(),
    city: Joi.string().min(2).max(100).required(),
    state: Joi.string().max(100).optional(),
    postal_code: Joi.string().max(20).optional(),
    country: Joi.string().max(100).optional(),
    property_type: Joi.string().valid('residential', 'commercial', 'mixed').required(),
    manager_id: Joi.string().uuid().optional(),
    images: Joi.array().items(Joi.string().uri()).optional(),
    amenities: Joi.array().items(Joi.string()).optional()
  }),
  
  updateProperty: Joi.object({
    name: Joi.string().min(2).max(255).optional(),
    description: Joi.string().max(1000).optional(),
    address: Joi.string().min(5).max(500).optional(),
    city: Joi.string().min(2).max(100).optional(),
    state: Joi.string().max(100).optional(),
    postal_code: Joi.string().max(20).optional(),
    country: Joi.string().max(100).optional(),
    property_type: Joi.string().valid('residential', 'commercial', 'mixed').optional(),
    manager_id: Joi.string().uuid().allow(null).optional(),
    images: Joi.array().items(Joi.string().uri()).optional(),
    amenities: Joi.array().items(Joi.string()).optional(),
    is_active: Joi.boolean().optional()
  }),
  
  // Unit schemas
  createUnit: Joi.object({
    property_id: Joi.string().uuid().required(),
    unit_number: Joi.string().min(1).max(50).required(),
    unit_type: Joi.string().valid('studio', '1br', '2br', '3br', '4br', 'office', 'retail', 'warehouse').required(),
    floor_number: Joi.number().integer().min(0).optional(),
    square_feet: Joi.number().positive().optional(),
    bedrooms: Joi.number().integer().min(0).optional(),
    bathrooms: Joi.number().min(0).optional(),
    monthly_rent: Joi.number().positive().required(),
    security_deposit: Joi.number().min(0).required(),
    description: Joi.string().max(1000).optional(),
    amenities: Joi.array().items(Joi.string()).optional(),
    images: Joi.array().items(Joi.string().uri()).optional()
  }),
  
  // Maintenance request schemas
  createMaintenanceRequest: Joi.object({
    unit_id: Joi.string().uuid().required(),
    title: Joi.string().min(5).max(255).required(),
    description: Joi.string().min(10).max(2000).required(),
    category: Joi.string().valid('plumbing', 'electrical', 'hvac', 'appliance', 'structural', 'pest_control', 'cleaning', 'security', 'other').required(),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent', 'emergency').required(),
    is_emergency: Joi.boolean().optional(),
    requires_tenant_access: Joi.boolean().optional(),
    preferred_contact_method: Joi.string().valid('phone', 'email', 'sms').optional()
  }),
  
  // Invoice schemas
  createInvoice: Joi.object({
    lease_id: Joi.string().uuid().required(),
    invoice_type: Joi.string().valid('rent', 'deposit', 'late_fee', 'utility', 'maintenance', 'other').required(),
    amount: Joi.number().positive().required(),
    tax_amount: Joi.number().min(0).optional(),
    due_date: Joi.date().iso().required(),
    description: Joi.string().max(1000).optional(),
    line_items: Joi.array().items(
      Joi.object({
        description: Joi.string().required(),
        quantity: Joi.number().positive().required(),
        unit_price: Joi.number().positive().required(),
        total: Joi.number().positive().required()
      })
    ).min(1).required()
  })
};
