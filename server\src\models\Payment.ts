import { executeQuery, executeTransaction } from '../config/database.js';
import { Payment, PaginationQuery } from '../types/index.js';
import { v4 as uuidv4 } from 'uuid';

export class PaymentModel {
  // Create a new payment
  static async create(paymentData: Partial<Payment>): Promise<Payment> {
    const id = uuidv4();
    const transactionId = await this.generateTransactionId();
    
    const queries = [
      {
        query: `
          INSERT INTO payments (
            id, transaction_id, invoice_id, tenant_id, amount, payment_method,
            payment_gateway, gateway_transaction_id, payment_date, status,
            reference_number, notes, gateway_response, fee_amount
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        params: [
          id,
          transactionId,
          paymentData.invoice_id,
          paymentData.tenant_id,
          paymentData.amount,
          paymentData.payment_method || 'bank_transfer',
          paymentData.payment_gateway || null,
          paymentData.gateway_transaction_id || null,
          paymentData.payment_date || new Date(),
          paymentData.status || 'pending',
          paymentData.reference_number || null,
          paymentData.notes || null,
          paymentData.gateway_response ? JSON.stringify(paymentData.gateway_response) : null,
          paymentData.fee_amount || 0
        ]
      }
    ];
    
    // If payment is successful, update invoice status
    if (paymentData.status === 'completed') {
      queries.push({
        query: `UPDATE invoices SET status = 'paid' WHERE id = ?`,
        params: [paymentData.invoice_id]
      });
    }
    
    await executeTransaction(queries);
    
    return await this.findById(id) as Payment;
  }

  // Generate unique transaction ID
  static async generateTransactionId(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const day = String(new Date().getDate()).padStart(2, '0');
    
    const query = `
      SELECT COUNT(*) as count 
      FROM payments 
      WHERE transaction_id LIKE ?
    `;
    
    const prefix = `TXN-${year}${month}${day}`;
    const result = await executeQuery(query, [`${prefix}%`]);
    const count = result[0].count + 1;
    
    return `${prefix}-${String(count).padStart(4, '0')}`;
  }

  // Find payment by ID
  static async findById(id: string): Promise<Payment | null> {
    const query = `
      SELECT p.*, 
             i.invoice_number, i.total_amount as invoice_amount, i.due_date,
             CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
             u.email as tenant_email,
             l.monthly_rent, un.unit_number, pr.name as property_name
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      LEFT JOIN tenants t ON p.tenant_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties pr ON un.property_id = pr.id
      WHERE p.id = ?
    `;
    
    const results = await executeQuery(query, [id]);
    
    if (results.length === 0) {
      return null;
    }
    
    const payment = results[0];
    
    // Parse JSON fields
    if (payment.gateway_response) {
      payment.gateway_response = JSON.parse(payment.gateway_response);
    }
    
    return payment;
  }

  // Update payment
  static async update(id: string, updateData: Partial<Payment>): Promise<Payment | null> {
    const allowedFields = [
      'status', 'gateway_transaction_id', 'reference_number', 'notes',
      'gateway_response', 'fee_amount'
    ];
    
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key) && updateData[key as keyof Payment] !== undefined) {
        if (key === 'gateway_response') {
          updates.push(`${key} = ?`);
          params.push(JSON.stringify(updateData[key as keyof Payment]));
        } else {
          updates.push(`${key} = ?`);
          params.push(updateData[key as keyof Payment]);
        }
      }
    });
    
    if (updates.length === 0) {
      return await this.findById(id);
    }
    
    params.push(id);
    
    const queries = [
      {
        query: `
          UPDATE payments 
          SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `,
        params
      }
    ];
    
    // If payment status changed to completed, update invoice
    if (updateData.status === 'completed') {
      const payment = await this.findById(id);
      if (payment && payment.invoice_id) {
        queries.push({
          query: `UPDATE invoices SET status = 'paid' WHERE id = ?`,
          params: [payment.invoice_id]
        });
      }
    }
    
    await executeTransaction(queries);
    return await this.findById(id);
  }

  // Get all payments with pagination and filtering
  static async findAll(
    options: PaginationQuery & {
      landlordId?: string;
      tenantId?: string;
      propertyId?: string;
      status?: string;
      paymentMethod?: string;
      dateFrom?: Date;
      dateTo?: Date;
    } = {}
  ): Promise<{ payments: Payment[]; total: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'payment_date',
      sortOrder = 'desc',
      search,
      landlordId,
      tenantId,
      propertyId,
      status,
      paymentMethod,
      dateFrom,
      dateTo
    } = options;
    
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ` AND pr.landlord_id = ?`;
      params.push(landlordId);
    }
    
    if (tenantId) {
      whereClause += ` AND p.tenant_id = ?`;
      params.push(tenantId);
    }
    
    if (propertyId) {
      whereClause += ` AND un.property_id = ?`;
      params.push(propertyId);
    }
    
    if (status) {
      whereClause += ` AND p.status = ?`;
      params.push(status);
    }
    
    if (paymentMethod) {
      whereClause += ` AND p.payment_method = ?`;
      params.push(paymentMethod);
    }
    
    if (dateFrom) {
      whereClause += ` AND p.payment_date >= ?`;
      params.push(dateFrom);
    }
    
    if (dateTo) {
      whereClause += ` AND p.payment_date <= ?`;
      params.push(dateTo);
    }
    
    if (search) {
      whereClause += ` AND (p.transaction_id LIKE ? OR p.reference_number LIKE ? OR CONCAT(u.first_name, ' ', u.last_name) LIKE ?)`;
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }
    
    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      LEFT JOIN tenants t ON p.tenant_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties pr ON un.property_id = pr.id
      ${whereClause}
    `;
    
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;
    
    // Get payments
    const query = `
      SELECT p.*, 
             i.invoice_number, i.total_amount as invoice_amount,
             CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
             u.email as tenant_email,
             un.unit_number, pr.name as property_name
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      LEFT JOIN tenants t ON p.tenant_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties pr ON un.property_id = pr.id
      ${whereClause}
      ORDER BY p.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const payments = await executeQuery(query, params);
    
    // Parse JSON fields
    payments.forEach((payment: any) => {
      if (payment.gateway_response) {
        payment.gateway_response = JSON.parse(payment.gateway_response);
      }
    });
    
    return { payments, total };
  }

  // Process M-Pesa payment
  static async processMpesaPayment(paymentData: {
    invoice_id: string;
    tenant_id: string;
    amount: number;
    phone_number: string;
  }): Promise<Payment> {
    // This would integrate with M-Pesa API
    // For now, we'll create a pending payment
    
    const payment = await this.create({
      ...paymentData,
      payment_method: 'mpesa',
      payment_gateway: 'safaricom',
      status: 'pending',
      notes: `M-Pesa payment initiated for phone: ${paymentData.phone_number}`
    });
    
    // Here you would make actual M-Pesa API call
    // const mpesaResponse = await mpesaAPI.initiatePayment(paymentData);
    
    return payment;
  }

  // Process bank transfer
  static async processBankTransfer(paymentData: {
    invoice_id: string;
    tenant_id: string;
    amount: number;
    reference_number: string;
    bank_name?: string;
  }): Promise<Payment> {
    return await this.create({
      ...paymentData,
      payment_method: 'bank_transfer',
      status: 'pending',
      notes: `Bank transfer from ${paymentData.bank_name || 'Unknown Bank'}`
    });
  }

  // Process card payment
  static async processCardPayment(paymentData: {
    invoice_id: string;
    tenant_id: string;
    amount: number;
    card_token: string;
    gateway: string;
  }): Promise<Payment> {
    // This would integrate with payment gateway (Stripe, Flutterwave, etc.)
    
    const payment = await this.create({
      ...paymentData,
      payment_method: 'card',
      payment_gateway: paymentData.gateway,
      status: 'pending'
    });
    
    // Here you would make actual gateway API call
    // const gatewayResponse = await paymentGateway.processPayment(paymentData);
    
    return payment;
  }

  // Get payment statistics
  static async getStats(landlordId?: string, dateFrom?: Date, dateTo?: Date): Promise<any> {
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ' AND pr.landlord_id = ?';
      params.push(landlordId);
    }
    
    if (dateFrom) {
      whereClause += ' AND p.payment_date >= ?';
      params.push(dateFrom);
    }
    
    if (dateTo) {
      whereClause += ' AND p.payment_date <= ?';
      params.push(dateTo);
    }
    
    const query = `
      SELECT 
        COUNT(*) as total_payments,
        COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as successful_payments,
        COUNT(CASE WHEN p.status = 'failed' THEN 1 END) as failed_payments,
        COUNT(CASE WHEN p.status = 'pending' THEN 1 END) as pending_payments,
        SUM(CASE WHEN p.status = 'completed' THEN p.amount ELSE 0 END) as total_amount_received,
        SUM(CASE WHEN p.status = 'pending' THEN p.amount ELSE 0 END) as total_amount_pending,
        AVG(CASE WHEN p.status = 'completed' THEN p.amount ELSE NULL END) as average_payment_amount,
        COUNT(CASE WHEN p.payment_method = 'mpesa' THEN 1 END) as mpesa_payments,
        COUNT(CASE WHEN p.payment_method = 'bank_transfer' THEN 1 END) as bank_payments,
        COUNT(CASE WHEN p.payment_method = 'card' THEN 1 END) as card_payments
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties pr ON un.property_id = pr.id
      ${whereClause}
    `;
    
    const results = await executeQuery(query, params);
    return results[0];
  }

  // Get payment methods summary
  static async getPaymentMethodsStats(landlordId?: string): Promise<any[]> {
    let whereClause = 'WHERE p.status = "completed"';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ' AND pr.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        p.payment_method,
        COUNT(*) as count,
        SUM(p.amount) as total_amount,
        AVG(p.amount) as average_amount
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties pr ON un.property_id = pr.id
      ${whereClause}
      GROUP BY p.payment_method
      ORDER BY total_amount DESC
    `;
    
    return await executeQuery(query, params);
  }

  // Reconcile payments (match bank statements with payments)
  static async reconcilePayments(reconciliationData: {
    payment_ids: string[];
    bank_statement_reference: string;
    reconciled_by: string;
  }): Promise<boolean> {
    const queries = reconciliationData.payment_ids.map(paymentId => ({
      query: `
        UPDATE payments 
        SET status = 'completed', 
            reference_number = ?,
            notes = CONCAT(COALESCE(notes, ''), ' - Reconciled by: ', ?),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND status = 'pending'
      `,
      params: [
        reconciliationData.bank_statement_reference,
        reconciliationData.reconciled_by,
        paymentId
      ]
    }));
    
    await executeTransaction(queries);
    
    // Update corresponding invoices to paid status
    for (const paymentId of reconciliationData.payment_ids) {
      const payment = await this.findById(paymentId);
      if (payment && payment.invoice_id) {
        await executeQuery(
          `UPDATE invoices SET status = 'paid' WHERE id = ?`,
          [payment.invoice_id]
        );
      }
    }
    
    return true;
  }
}
