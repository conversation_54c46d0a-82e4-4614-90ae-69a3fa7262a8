import React, { useState, useEffect } from 'react';
import { 
  Home, 
  CreditCard, 
  FileText, 
  Wrench,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  Phone,
  MessageSquare
} from 'lucide-react';
import { dashboardAPI } from '../../services/api';

interface TenantDashboard {
  currentLease: any;
  nextPaymentDue: any;
  recentPayments: any[];
  maintenanceRequests: any[];
  announcements: any[];
}

const TenantDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<TenantDashboard | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTenantData();
  }, []);

  const fetchTenantData = async () => {
    try {
      setLoading(true);
      
      // Mock tenant-specific data (would come from API)
      const mockData: TenantDashboard = {
        currentLease: {
          property: 'Sunset Apartments',
          unit: 'A1',
          monthlyRent: 30000,
          leaseStart: '2024-01-01',
          leaseEnd: '2024-12-31',
          securityDeposit: 60000
        },
        nextPaymentDue: {
          amount: 30000,
          dueDate: '2024-02-05',
          type: 'Monthly Rent',
          status: 'pending'
        },
        recentPayments: [
          { id: 1, amount: 30000, date: '2024-01-05', method: 'M-Pesa', status: 'completed' },
          { id: 2, amount: 30000, date: '2023-12-05', method: 'Bank Transfer', status: 'completed' },
          { id: 3, amount: 30000, date: '2023-11-05', method: 'M-Pesa', status: 'completed' }
        ],
        maintenanceRequests: [
          { id: 1, issue: 'Leaking faucet in kitchen', status: 'in_progress', date: '2024-01-15', priority: 'medium' },
          { id: 2, issue: 'Broken light bulb in bedroom', status: 'completed', date: '2024-01-10', priority: 'low' }
        ],
        announcements: [
          { id: 1, title: 'Water Maintenance Scheduled', message: 'Water will be shut off on Feb 10 from 9 AM to 2 PM', date: '2024-01-20' },
          { id: 2, title: 'New Parking Rules', message: 'Please ensure vehicles are parked in designated spots only', date: '2024-01-18' }
        ]
      };
      
      setDashboardData(mockData);
      
    } catch (error) {
      console.error('Failed to fetch tenant data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle size={16} className="text-green-600" />;
      case 'in_progress': return <Clock size={16} className="text-blue-600" />;
      case 'pending': return <AlertCircle size={16} className="text-yellow-600" />;
      default: return <Clock size={16} className="text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back! Here's your rental information.</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <CreditCard size={16} className="mr-2" />
            Pay Rent
          </button>
          <button className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center">
            <Wrench size={16} className="mr-2" />
            Request Maintenance
          </button>
        </div>
      </div>

      {/* Current Lease Info */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center mb-4">
          <Home size={24} className="text-blue-600 mr-3" />
          <h2 className="text-xl font-semibold text-gray-900">Current Lease</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <p className="text-sm font-medium text-gray-600">Property</p>
            <p className="text-lg font-semibold text-gray-900">{dashboardData?.currentLease.property}</p>
            <p className="text-sm text-gray-600">Unit {dashboardData?.currentLease.unit}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Monthly Rent</p>
            <p className="text-lg font-semibold text-gray-900">
              KES {dashboardData?.currentLease.monthlyRent.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Lease Period</p>
            <p className="text-lg font-semibold text-gray-900">
              {formatDate(dashboardData?.currentLease.leaseStart)} - {formatDate(dashboardData?.currentLease.leaseEnd)}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Security Deposit</p>
            <p className="text-lg font-semibold text-gray-900">
              KES {dashboardData?.currentLease.securityDeposit.toLocaleString()}
            </p>
          </div>
        </div>
      </div>

      {/* Payment Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Next Payment Due */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Next Payment Due</h3>
            <DollarSign size={20} className="text-green-600" />
          </div>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-blue-900">{dashboardData?.nextPaymentDue.type}</span>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(dashboardData?.nextPaymentDue.status)}`}>
                  {dashboardData?.nextPaymentDue.status}
                </span>
              </div>
              <p className="text-2xl font-bold text-blue-900">
                KES {dashboardData?.nextPaymentDue.amount.toLocaleString()}
              </p>
              <p className="text-sm text-blue-700">
                Due: {formatDate(dashboardData?.nextPaymentDue.dueDate)}
              </p>
            </div>
            <button className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
              Pay Now
            </button>
          </div>
        </div>

        {/* Recent Payments */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Recent Payments</h3>
            <CreditCard size={20} className="text-purple-600" />
          </div>
          <div className="space-y-3">
            {dashboardData?.recentPayments.map((payment) => (
              <div key={payment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">KES {payment.amount.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">{formatDate(payment.date)} • {payment.method}</p>
                </div>
                <div className="flex items-center">
                  {getStatusIcon(payment.status)}
                  <span className="ml-2 text-sm font-medium text-gray-700 capitalize">{payment.status}</span>
                </div>
              </div>
            ))}
          </div>
          <button className="w-full mt-4 text-blue-600 hover:text-blue-700 font-medium text-sm">
            View All Payments
          </button>
        </div>
      </div>

      {/* Maintenance Requests */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Maintenance Requests</h3>
          <Wrench size={20} className="text-orange-600" />
        </div>
        <div className="space-y-3">
          {dashboardData?.maintenanceRequests.map((request) => (
            <div key={request.id} className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  {getStatusIcon(request.status)}
                  <span className="ml-2 font-medium text-gray-900">{request.issue}</span>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(request.status)}`}>
                  {request.status.replace('_', ' ')}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>Submitted: {formatDate(request.date)}</span>
                <span className="capitalize">Priority: {request.priority}</span>
              </div>
            </div>
          ))}
        </div>
        <button className="w-full mt-4 bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition-colors font-medium">
          Submit New Request
        </button>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <CreditCard size={20} className="text-blue-600 mb-2" />
              <p className="font-medium text-gray-900">Pay Rent</p>
              <p className="text-sm text-gray-600">Make payment</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <Wrench size={20} className="text-orange-600 mb-2" />
              <p className="font-medium text-gray-900">Maintenance</p>
              <p className="text-sm text-gray-600">Report issue</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <FileText size={20} className="text-green-600 mb-2" />
              <p className="font-medium text-gray-900">Documents</p>
              <p className="text-sm text-gray-600">View lease</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <Phone size={20} className="text-purple-600 mb-2" />
              <p className="font-medium text-gray-900">Contact</p>
              <p className="text-sm text-gray-600">Get help</p>
            </button>
          </div>
        </div>

        {/* Announcements */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Announcements</h3>
            <MessageSquare size={20} className="text-blue-600" />
          </div>
          <div className="space-y-3">
            {dashboardData?.announcements.map((announcement) => (
              <div key={announcement.id} className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="font-medium text-blue-900">{announcement.title}</p>
                <p className="text-sm text-blue-700 mt-1">{announcement.message}</p>
                <p className="text-xs text-blue-600 mt-2">{formatDate(announcement.date)}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TenantDashboard;
