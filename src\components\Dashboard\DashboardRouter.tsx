import React from 'react';
import { useAuth } from '../../hooks/useAuth';
import LandlordDashboard from './LandlordDashboard';
import PropertyManagerDashboard from './PropertyManagerDashboard';
import TenantDashboard from './TenantDashboard';
import CaretakerDashboard from './CaretakerDashboard';

const DashboardRouter: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-gray-600">Please log in to access your dashboard.</p>
        </div>
      </div>
    );
  }

  switch (user.role) {
    case 'landlord':
      return <LandlordDashboard />;
    case 'property_manager':
      return <PropertyManagerDashboard />;
    case 'tenant':
      return <TenantDashboard />;
    case 'caretaker':
      return <CaretakerDashboard />;
    default:
      return (
        <div className="p-6">
          <div className="text-center">
            <p className="text-gray-600">Unknown user role. Please contact support.</p>
          </div>
        </div>
      );
  }
};

export default DashboardRouter;
