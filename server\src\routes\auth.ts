import { Router, Request, Response } from 'express';
import { UserModel } from '../models/User.js';
import { 
  generateToken, 
  generateRefreshToken, 
  verifyRefreshToken,
  authenticateToken 
} from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { LoginRequest, CreateUserRequest, AuthResponse } from '../types/index.js';

const router = Router();

// Register new user
router.post('/register', 
  validate(schemas.createUser),
  asyncHandler(async (req: Request, res: Response) => {
    const userData: CreateUserRequest = req.body;
    
    // Check if email already exists
    const existingUser = await UserModel.findByEmail(userData.email);
    if (existingUser) {
      throw new AppError('Email already registered', 409);
    }
    
    // Create user
    const user = await UserModel.create(userData);
    
    // Generate tokens
    const token = generateToken(user);
    const refreshToken = generateRefreshToken(user);
    
    // Remove password from response
    const { password_hash, ...userResponse } = user;
    
    const response: AuthResponse = {
      user: userResponse,
      token,
      refreshToken
    };
    
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: response
    });
  })
);

// Login user
router.post('/login',
  validate(schemas.login),
  asyncHandler(async (req: Request, res: Response) => {
    const { email, password }: LoginRequest = req.body;
    
    // Find user by email
    const user = await UserModel.findByEmail(email);
    if (!user) {
      throw new AppError('Invalid email or password', 401);
    }
    
    // Verify password
    const isPasswordValid = await UserModel.verifyPassword(password, user.password_hash!);
    if (!isPasswordValid) {
      throw new AppError('Invalid email or password', 401);
    }
    
    // Update last login
    await UserModel.updateLastLogin(user.id);
    
    // Generate tokens
    const token = generateToken(user);
    const refreshToken = generateRefreshToken(user);
    
    // Remove password from response
    const { password_hash, ...userResponse } = user;
    
    const response: AuthResponse = {
      user: userResponse,
      token,
      refreshToken
    };
    
    res.json({
      success: true,
      message: 'Login successful',
      data: response
    });
  })
);

// Refresh token
router.post('/refresh',
  asyncHandler(async (req: Request, res: Response) => {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      throw new AppError('Refresh token is required', 400);
    }
    
    // Verify refresh token
    const user = await verifyRefreshToken(refreshToken);
    if (!user) {
      throw new AppError('Invalid refresh token', 401);
    }
    
    // Generate new tokens
    const newToken = generateToken(user);
    const newRefreshToken = generateRefreshToken(user);
    
    // Remove password from response
    const { password_hash, ...userResponse } = user;
    
    const response: AuthResponse = {
      user: userResponse,
      token: newToken,
      refreshToken: newRefreshToken
    };
    
    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: response
    });
  })
);

// Get current user profile
router.get('/me',
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    // User is already attached to req by authenticateToken middleware
    const { password_hash, ...userResponse } = req.user!;
    
    res.json({
      success: true,
      data: userResponse
    });
  })
);

// Update current user profile
router.put('/me',
  authenticateToken,
  validate(schemas.updateUser),
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const updateData = req.body;
    
    // Update user
    const updatedUser = await UserModel.update(userId, updateData);
    
    if (!updatedUser) {
      throw new AppError('User not found', 404);
    }
    
    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: updatedUser
    });
  })
);

// Change password
router.put('/change-password',
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      throw new AppError('Current password and new password are required', 400);
    }
    
    if (newPassword.length < 8) {
      throw new AppError('New password must be at least 8 characters long', 400);
    }
    
    // Get user with password
    const user = await UserModel.findByEmail(req.user!.email);
    if (!user) {
      throw new AppError('User not found', 404);
    }
    
    // Verify current password
    const isCurrentPasswordValid = await UserModel.verifyPassword(currentPassword, user.password_hash!);
    if (!isCurrentPasswordValid) {
      throw new AppError('Current password is incorrect', 400);
    }
    
    // Update password
    await UserModel.updatePassword(user.id, newPassword);
    
    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  })
);

// Logout (client-side token removal)
router.post('/logout',
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    // In a more sophisticated setup, you might want to blacklist the token
    // For now, we'll just return a success message
    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  })
);

export default router;
