import { executeQuery, executeTransaction } from '../config/database';
import { Payment, PaymentMethod } from '../types';

export class PaymentService {
  // Process payment
  static async processPayment(paymentData: {
    invoiceId: string;
    tenantId: string;
    amount: number;
    method: PaymentMethod;
    transactionReference?: string;
    mpesaReceiptNumber?: string;
    gatewayResponse?: any;
  }): Promise<string> {
    const queries = [
      // Insert payment record
      {
        query: `
          INSERT INTO payments (invoice_id, tenant_id, amount, payment_method,
                               transaction_reference, mpesa_receipt_number, payment_date,
                               status, gateway_response, net_amount)
          VALUES (?, ?, ?, ?, ?, ?, NOW(), 'completed', ?, ?)
        `,
        params: [
          paymentData.invoiceId,
          paymentData.tenantId,
          paymentData.amount,
          paymentData.method,
          paymentData.transactionReference || null,
          paymentData.mpesaReceiptNumber || null,
          JSON.stringify(paymentData.gatewayResponse || {}),
          paymentData.amount // Assuming no fees for now
        ]
      },
      // Update invoice paid amount and status
      {
        query: `
          UPDATE invoices 
          SET paid_amount = paid_amount + ?,
              payment_date = CASE 
                WHEN paid_amount + ? >= total_amount THEN NOW()
                ELSE payment_date
              END,
              status = CASE 
                WHEN paid_amount + ? >= total_amount THEN 'paid'
                WHEN paid_amount + ? > 0 THEN 'partial'
                ELSE status
              END
          WHERE id = ?
        `,
        params: [
          paymentData.amount,
          paymentData.amount,
          paymentData.amount,
          paymentData.amount,
          paymentData.invoiceId
        ]
      }
    ];

    const results = await executeTransaction(queries);
    return results[0].insertId;
  }

  // Get payments by tenant
  static async getPaymentsByTenant(tenantId: string, limit: number = 50): Promise<Payment[]> {
    const query = `
      SELECT p.*, 
             i.invoice_number,
             i.amount as invoice_amount,
             u.unit_number,
             pr.name as property_name
      FROM payments p
      JOIN invoices i ON p.invoice_id = i.id
      JOIN leases l ON i.lease_id = l.id
      JOIN units u ON l.unit_id = u.id
      JOIN properties pr ON u.property_id = pr.id
      WHERE p.tenant_id = ?
      ORDER BY p.payment_date DESC
      LIMIT ?
    `;
    
    const results = await executeQuery(query, [tenantId, limit]);
    return results.map(this.mapDatabaseToPayment);
  }

  // Get payments by property
  static async getPaymentsByProperty(propertyId: string, startDate?: Date, endDate?: Date): Promise<Payment[]> {
    let query = `
      SELECT p.*, 
             i.invoice_number,
             i.amount as invoice_amount,
             u.unit_number,
             pr.name as property_name,
             CONCAT(usr.first_name, ' ', usr.last_name) as tenant_name
      FROM payments p
      JOIN invoices i ON p.invoice_id = i.id
      JOIN leases l ON i.lease_id = l.id
      JOIN units u ON l.unit_id = u.id
      JOIN properties pr ON u.property_id = pr.id
      JOIN tenants t ON p.tenant_id = t.id
      JOIN users usr ON t.user_id = usr.id
      WHERE pr.id = ?
    `;
    
    const params = [propertyId];
    
    if (startDate) {
      query += ' AND p.payment_date >= ?';
      params.push(startDate);
    }
    
    if (endDate) {
      query += ' AND p.payment_date <= ?';
      params.push(endDate);
    }
    
    query += ' ORDER BY p.payment_date DESC';
    
    const results = await executeQuery(query, params);
    return results.map(this.mapDatabaseToPayment);
  }

  // Reconcile M-Pesa payments
  static async reconcileMpesaPayments(mpesaTransactions: any[]): Promise<{
    matched: number;
    unmatched: number;
    errors: string[];
  }> {
    let matched = 0;
    let unmatched = 0;
    const errors: string[] = [];

    for (const transaction of mpesaTransactions) {
      try {
        // Find matching payment by M-Pesa receipt number or transaction reference
        const query = `
          SELECT id FROM payments
          WHERE (mpesa_receipt_number = ? OR transaction_reference = ?)
            AND reconciliation_status = 'pending'
        `;
        
        const [payment] = await executeQuery(query, [
          transaction.receipt_number,
          transaction.transaction_id
        ]);

        if (payment) {
          // Update reconciliation status
          await executeQuery(
            'UPDATE payments SET reconciliation_status = ?, reconciled_at = NOW() WHERE id = ?',
            ['matched', payment.id]
          );
          matched++;
        } else {
          unmatched++;
        }
      } catch (error) {
        errors.push(`Error processing transaction ${transaction.receipt_number}: ${error.message}`);
      }
    }

    return { matched, unmatched, errors };
  }

  // Get payment statistics
  static async getPaymentStats(propertyId?: string, startDate?: Date, endDate?: Date): Promise<{
    totalAmount: number;
    totalTransactions: number;
    averageAmount: number;
    methodBreakdown: { [key: string]: { count: number; amount: number } };
  }> {
    let query = `
      SELECT 
        COUNT(*) as total_transactions,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        payment_method,
        COUNT(*) as method_count,
        SUM(amount) as method_amount
      FROM payments p
    `;
    
    const params: any[] = [];
    const conditions: string[] = [];

    if (propertyId) {
      query += `
        JOIN invoices i ON p.invoice_id = i.id
        JOIN leases l ON i.lease_id = l.id
        JOIN units u ON l.unit_id = u.id
      `;
      conditions.push('u.property_id = ?');
      params.push(propertyId);
    }

    if (startDate) {
      conditions.push('p.payment_date >= ?');
      params.push(startDate);
    }

    if (endDate) {
      conditions.push('p.payment_date <= ?');
      params.push(endDate);
    }

    conditions.push("p.status = 'completed'");

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' GROUP BY payment_method';

    const results = await executeQuery(query, params);
    
    const stats = {
      totalAmount: 0,
      totalTransactions: 0,
      averageAmount: 0,
      methodBreakdown: {} as { [key: string]: { count: number; amount: number } }
    };

    for (const row of results) {
      stats.totalAmount += parseFloat(row.method_amount);
      stats.totalTransactions += row.method_count;
      stats.methodBreakdown[row.payment_method] = {
        count: row.method_count,
        amount: parseFloat(row.method_amount)
      };
    }

    stats.averageAmount = stats.totalTransactions > 0 ? stats.totalAmount / stats.totalTransactions : 0;

    return stats;
  }

  // Map database result to Payment object
  private static mapDatabaseToPayment(row: any): Payment {
    return {
      id: row.id,
      invoiceId: row.invoice_id,
      tenantId: row.tenant_id,
      amount: parseFloat(row.amount),
      method: row.payment_method,
      transactionRef: row.transaction_reference,
      mpesaReceiptNumber: row.mpesa_receipt_number,
      date: new Date(row.payment_date),
      status: row.status,
      gatewayResponse: row.gateway_response ? JSON.parse(row.gateway_response) : undefined,
      reconciliationStatus: row.reconciliation_status,
      reconciledAt: row.reconciled_at ? new Date(row.reconciled_at) : undefined,
      reconciledBy: row.reconciled_by,
      fees: parseFloat(row.fees || '0'),
      netAmount: parseFloat(row.net_amount),
      notes: row.notes,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      // Additional fields from joins
      invoiceNumber: row.invoice_number,
      invoiceAmount: parseFloat(row.invoice_amount || '0'),
      unitNumber: row.unit_number,
      propertyName: row.property_name,
      tenantName: row.tenant_name
    };
  }
}