import { User, AuthResponse, LoginRequest, CreateUserRequest } from '../types';

// API configuration
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api/v1';

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  const data = await response.json();
  
  if (!response.ok) {
    const error = data.error || response.statusText;
    throw new Error(error);
  }
  
  return data;
};

// Get auth token from local storage
const getToken = (): string | null => {
  return localStorage.getItem('token');
};

// API request with auth header
const apiRequest = async (
  endpoint: string, 
  method: string = 'GET', 
  body?: any,
  customHeaders?: Record<string, string>
) => {
  const token = getToken();
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...customHeaders
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  const config: RequestInit = {
    method,
    headers,
    credentials: 'include'
  };
  
  if (body && method !== 'GET') {
    config.body = JSON.stringify(body);
  }
  
  const response = await fetch(`${API_URL}${endpoint}`, config);
  return handleResponse(response);
};

// Auth API
export const authAPI = {
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await apiRequest('/auth/login', 'POST', credentials);
    
    // Store tokens in localStorage
    localStorage.setItem('token', response.data.token);
    localStorage.setItem('refreshToken', response.data.refreshToken);
    
    return response.data;
  },
  
  register: async (userData: CreateUserRequest): Promise<AuthResponse> => {
    const response = await apiRequest('/auth/register', 'POST', userData);
    
    // Store tokens in localStorage
    localStorage.setItem('token', response.data.token);
    localStorage.setItem('refreshToken', response.data.refreshToken);
    
    return response.data;
  },
  
  logout: async (): Promise<void> => {
    await apiRequest('/auth/logout', 'POST');
    
    // Remove tokens from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  },
  
  refreshToken: async (): Promise<AuthResponse> => {
    const refreshToken = localStorage.getItem('refreshToken');
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }
    
    const response = await apiRequest('/auth/refresh', 'POST', { refreshToken });
    
    // Update tokens in localStorage
    localStorage.setItem('token', response.data.token);
    localStorage.setItem('refreshToken', response.data.refreshToken);
    
    return response.data;
  },
  
  getCurrentUser: async (): Promise<User> => {
    const response = await apiRequest('/auth/me');
    return response.data;
  },
  
  updateProfile: async (userData: Partial<User>): Promise<User> => {
    const response = await apiRequest('/auth/me', 'PUT', userData);
    return response.data;
  },
  
  changePassword: async (currentPassword: string, newPassword: string): Promise<void> => {
    await apiRequest('/auth/change-password', 'PUT', { currentPassword, newPassword });
  }
};

// Properties API
export const propertiesAPI = {
  getAll: async (params?: Record<string, any>) => {
    const queryParams = params ? 
      `?${new URLSearchParams(params).toString()}` : '';
    
    const response = await apiRequest(`/properties${queryParams}`);
    return response;
  },
  
  getById: async (id: string) => {
    const response = await apiRequest(`/properties/${id}`);
    return response.data;
  },
  
  create: async (propertyData: any) => {
    const response = await apiRequest('/properties', 'POST', propertyData);
    return response.data;
  },
  
  update: async (id: string, propertyData: any) => {
    const response = await apiRequest(`/properties/${id}`, 'PUT', propertyData);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiRequest(`/properties/${id}`, 'DELETE');
    return response;
  },
  
  getStats: async (id: string) => {
    const response = await apiRequest(`/properties/${id}/stats`);
    return response.data;
  }
};

// Units API
export const unitsAPI = {
  getByPropertyId: async (propertyId: string, params?: Record<string, any>) => {
    const queryParams = params ? 
      `?${new URLSearchParams(params).toString()}` : '';
    
    const response = await apiRequest(`/units/property/${propertyId}${queryParams}`);
    return response;
  },
  
  getById: async (id: string) => {
    const response = await apiRequest(`/units/${id}`);
    return response.data;
  },
  
  create: async (unitData: any) => {
    const response = await apiRequest('/units', 'POST', unitData);
    return response.data;
  },
  
  update: async (id: string, unitData: any) => {
    const response = await apiRequest(`/units/${id}`, 'PUT', unitData);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiRequest(`/units/${id}`, 'DELETE');
    return response;
  },
  
  getAvailable: async (propertyId?: string) => {
    const queryParams = propertyId ? `?propertyId=${propertyId}` : '';
    const response = await apiRequest(`/units/available/list${queryParams}`);
    return response.data;
  }
};

// Dashboard API
export const dashboardAPI = {
  getStats: async () => {
    const response = await apiRequest('/dashboard/stats');
    return response.data;
  },
  
  getActivities: async (limit: number = 10) => {
    const response = await apiRequest(`/dashboard/activities?limit=${limit}`);
    return response.data;
  }
};

// Maintenance API
export const maintenanceAPI = {
  getAll: async (params?: Record<string, any>) => {
    const queryParams = params ? 
      `?${new URLSearchParams(params).toString()}` : '';
    
    const response = await apiRequest(`/maintenance${queryParams}`);
    return response;
  },
  
  getById: async (id: string) => {
    const response = await apiRequest(`/maintenance/${id}`);
    return response.data;
  },
  
  create: async (requestData: any) => {
    const response = await apiRequest('/maintenance', 'POST', requestData);
    return response.data;
  },
  
  update: async (id: string, requestData: any) => {
    const response = await apiRequest(`/maintenance/${id}`, 'PUT', requestData);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiRequest(`/maintenance/${id}`, 'DELETE');
    return response;
  }
};

// Invoices API
export const invoicesAPI = {
  getAll: async (params?: Record<string, any>) => {
    const queryParams = params ? 
      `?${new URLSearchParams(params).toString()}` : '';
    
    const response = await apiRequest(`/invoices${queryParams}`);
    return response;
  },
  
  getById: async (id: string) => {
    const response = await apiRequest(`/invoices/${id}`);
    return response.data;
  },
  
  create: async (invoiceData: any) => {
    const response = await apiRequest('/invoices', 'POST', invoiceData);
    return response.data;
  },
  
  update: async (id: string, invoiceData: any) => {
    const response = await apiRequest(`/invoices/${id}`, 'PUT', invoiceData);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiRequest(`/invoices/${id}`, 'DELETE');
    return response;
  },
  
  generateMonthly: async () => {
    const response = await apiRequest('/invoices/generate-monthly', 'POST');
    return response;
  },
  
  downloadPdf: async (id: string) => {
    const response = await fetch(`${API_URL}/invoices/${id}/pdf`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to download PDF');
    }
    
    return response.blob();
  }
};

// Payments API
export const paymentsAPI = {
  getAll: async (params?: Record<string, any>) => {
    const queryParams = params ? 
      `?${new URLSearchParams(params).toString()}` : '';
    
    const response = await apiRequest(`/payments${queryParams}`);
    return response;
  },
  
  getById: async (id: string) => {
    const response = await apiRequest(`/payments/${id}`);
    return response.data;
  },
  
  create: async (paymentData: any) => {
    const response = await apiRequest('/payments', 'POST', paymentData);
    return response.data;
  },
  
  initiatePayment: async (invoiceId: string, method: string, amount: number) => {
    const response = await apiRequest('/payments/initiate', 'POST', {
      invoiceId,
      method,
      amount
    });
    return response.data;
  }
};

// Export all APIs
export default {
  auth: authAPI,
  properties: propertiesAPI,
  units: unitsAPI,
  dashboard: dashboardAPI,
  maintenance: maintenanceAPI,
  invoices: invoicesAPI,
  payments: paymentsAPI
};
