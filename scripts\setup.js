#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up PropertyPro Enterprise Development Environment...\n');

// Check if Node.js version is compatible
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.error('❌ Node.js version 16 or higher is required. Current version:', nodeVersion);
  process.exit(1);
}

console.log('✅ Node.js version check passed:', nodeVersion);

// Function to run command and handle errors
function runCommand(command, description) {
  try {
    console.log(`📦 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

// Function to copy file if it doesn't exist
function copyFileIfNotExists(source, destination, description) {
  if (!fs.existsSync(destination)) {
    try {
      fs.copyFileSync(source, destination);
      console.log(`✅ ${description} created: ${destination}`);
    } catch (error) {
      console.log(`⚠️  Could not create ${description}: ${error.message}`);
    }
  } else {
    console.log(`ℹ️  ${description} already exists: ${destination}`);
  }
}

// Install frontend dependencies
runCommand('npm install', 'Installing frontend dependencies');

// Install backend dependencies
runCommand('cd server && npm install', 'Installing backend dependencies');

// Setup environment files
console.log('📝 Setting up environment files...');

copyFileIfNotExists('.env.example', '.env', 'Frontend environment file');
copyFileIfNotExists('server/.env.example', 'server/.env', 'Backend environment file');

// Create .env.local for frontend development
const frontendEnvLocal = `# Frontend Environment Variables

# API Configuration
VITE_API_URL=http://localhost:5000/api/v1

# App Configuration
VITE_APP_NAME=PropertyPro Enterprise
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_NOTIFICATIONS=true

# Development
VITE_DEBUG=true`;

if (!fs.existsSync('.env.local')) {
  fs.writeFileSync('.env.local', frontendEnvLocal);
  console.log('✅ Frontend .env.local created');
} else {
  console.log('ℹ️  Frontend .env.local already exists');
}

// Create uploads directory for backend
const uploadsDir = path.join('server', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('✅ Backend uploads directory created');
} else {
  console.log('ℹ️  Backend uploads directory already exists');
}

// Create logs directory for backend
const logsDir = path.join('server', 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('✅ Backend logs directory created');
} else {
  console.log('ℹ️  Backend logs directory already exists');
}

console.log('\n🎉 Setup completed successfully!\n');

console.log('📋 Next steps:');
console.log('1. Configure your database connection in server/.env');
console.log('2. Create MySQL database: property_management');
console.log('3. Import database schema: mysql -u root -p property_management < supabase/migrations/20250710060353_old_dream.sql');
console.log('4. Start backend server: cd server && npm run dev');
console.log('5. Start frontend server: npm run dev');
console.log('\n🌟 Happy coding!');
