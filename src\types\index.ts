export interface User {
  id: string;
  name: string;
  email: string;
  role: 'landlord' | 'property_manager' | 'caretaker' | 'tenant';
  avatar?: string;
  properties?: string[];
}

export interface Property {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state?: string;
  postalCode?: string;
  country?: string;
  type: 'residential' | 'commercial';
  totalUnits?: number;
  landlordId: string;
  managerId?: string;
  images?: string[];
  amenities?: string[];
  isActive?: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface Unit {
  id: string;
  propertyId: string;
  unitNumber: string;
  type: 'studio' | '1br' | '2br' | '3br' | '4br' | 'office' | 'retail' | 'warehouse';
  floorNumber?: number;
  squareFeet?: number;
  bedrooms?: number;
  bathrooms?: number;
  rent: number;
  deposit: number;
  description?: string;
  amenities?: string[];
  images?: string[];
  isOccupied: boolean;
  isAvailable?: boolean;
  tenant?: Tenant;
  lease?: {
    startDate: Date;
    endDate?: Date;
    status: string;
  };
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Tenant {
  id: string;
  userId?: string;
  name: string;
  email: string;
  phone: string;
  emergencyContact: string;
  emergencyContactName?: string;
  emergencyContactRelationship?: string;
  employer?: string;
  monthlyIncome?: number;
  idNumber?: string;
  idDocumentUrl?: string;
  backgroundCheckStatus?: 'pending' | 'approved' | 'rejected';
  backgroundCheckDate?: Date;
  moveInDate?: Date;
  moveOutDate?: Date;
  notes?: string;
  avatar?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Lease {
  id: string;
  unitId: string;
  tenantId: string;
  leaseType: 'fixed' | 'month_to_month' | 'commercial';
  startDate: Date;
  endDate?: Date;
  monthlyRent: number;
  deposit: number;
  lateFeeAmount?: number;
  lateFeeType?: 'flat' | 'percentage';
  gracePeriodDays?: number;
  leaseDocumentUrl?: string;
  status: 'active' | 'expired' | 'terminated';
  terminationDate?: Date;
  terminationReason?: string;
  autoRenew?: boolean;
  renewalNoticeDays?: number;
  specialTerms?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface InvoiceLineItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  leaseId: string;
  tenantId: string;
  type: 'rent' | 'deposit' | 'late_fee' | 'utility' | 'maintenance' | 'other';
  amount: number;
  taxAmount: number;
  totalAmount: number;
  dueDate: Date;
  issueDate: Date;
  description?: string;
  lineItems: InvoiceLineItem[];
  status: 'pending' | 'paid' | 'overdue' | 'partial';
  paidAmount: number;
  paymentDate?: Date;
  lateFee: number;
  pdfUrl?: string;
  reminderSentCount?: number;
  lastReminderSent?: Date;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // Additional fields for display
  unitNumber?: string;
  propertyName?: string;
  tenantName?: string;
  tenantEmail?: string;
  tenantPhone?: string;
  daysOverdue?: number;
}

export type PaymentMethod = 'mpesa' | 'bank_transfer' | 'card' | 'cash' | 'cheque';

export interface Payment {
  id: string;
  invoiceId: string;
  tenantId: string;
  amount: number;
  method: PaymentMethod;
  transactionRef?: string;
  mpesaReceiptNumber?: string;
  date: Date;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  gatewayResponse?: any;
  reconciliationStatus?: 'pending' | 'matched' | 'unmatched';
  reconciledAt?: Date;
  reconciledBy?: string;
  fees: number;
  netAmount: number;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // Additional fields for display
  invoiceNumber?: string;
  invoiceAmount?: number;
  unitNumber?: string;
  propertyName?: string;
  tenantName?: string;
}

export interface MaintenanceRequest {
  id: string;
  requestNumber: string;
  unitId: string;
  tenantId?: string;
  reportedBy: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'submitted' | 'acknowledged' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';
  category: 'plumbing' | 'electrical' | 'hvac' | 'appliance' | 'structural' | 'other';
  createdAt: Date;
  updatedAt: Date;
  assignedTo?: string;
  estimatedCost?: number;
  actualCost?: number;
  scheduledDate?: Date;
  completedDate?: Date;
  images?: string[];
  workNotes?: string;
  tenantSatisfactionRating?: number;
  tenantFeedback?: string;
  isEmergency?: boolean;
  requiresTenantAccess?: boolean;
  preferredContactMethod?: 'phone' | 'email' | 'sms';
  // Additional fields for display
  unitNumber?: string;
  propertyName?: string;
  reportedByName?: string;
  tenantName?: string;
  tenantPhone?: string;
  assignedToName?: string;
  assignedToPhone?: string;
}

export interface WorkOrder {
  id: string;
  workOrderNumber: string;
  maintenanceRequestId: string;
  assignedTo: string;
  contractorName?: string;
  contractorPhone?: string;
  contractorEmail?: string;
  scheduledStart?: Date;
  scheduledEnd?: Date;
  actualStart?: Date;
  actualEnd?: Date;
  materialsNeeded?: string;
  materialsCost?: number;
  laborCost?: number;
  totalCost?: number;
  workDescription?: string;
  completionNotes?: string;
  beforeImages?: string[];
  afterImages?: string[];
  status: 'created' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PropertyStats {
  totalUnits: number;
  occupiedUnits: number;
  vacantUnits: number;
  occupancyRate: number;
  potentialRevenue: number;
  actualRevenue: number;
  averageRent: number;
}

export interface DashboardStats {
  totalProperties: number;
  totalUnits: number;
  occupiedUnits: number;
  occupancyRate: number;
  monthlyRevenue: number;
  pendingInvoices: number;
  overdueInvoices: number;
  maintenanceRequests: number;
  urgentMaintenance: number;
}