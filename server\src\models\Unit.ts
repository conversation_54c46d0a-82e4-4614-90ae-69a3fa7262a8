import { executeQuery, executeTransaction } from '../config/database.js';
import { Unit, CreateUnitRequest, PaginationQuery } from '../types/index.js';
import { v4 as uuidv4 } from 'uuid';

export class UnitModel {
  // Create a new unit
  static async create(unitData: CreateUnitRequest): Promise<Unit> {
    const id = uuidv4();
    
    const query = `
      INSERT INTO units (
        id, property_id, unit_number, unit_type, floor_number, square_feet,
        bedrooms, bathrooms, monthly_rent, security_deposit, description,
        amenities, images, is_occupied, is_available
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, FALSE, TRUE)
    `;
    
    const params = [
      id,
      unitData.property_id,
      unitData.unit_number,
      unitData.unit_type,
      unitData.floor_number || null,
      unitData.square_feet || null,
      unitData.bedrooms || 0,
      unitData.bathrooms || 0,
      unitData.monthly_rent,
      unitData.security_deposit,
      unitData.description || null,
      unitData.amenities ? JSON.stringify(unitData.amenities) : null,
      unitData.images ? JSON.stringify(unitData.images) : null
    ];
    
    await executeQuery(query, params);
    
    // Update property total_units count
    await this.updatePropertyUnitCount(unitData.property_id);
    
    return await this.findById(id) as Unit;
  }

  // Find unit by ID
  static async findById(id: string): Promise<Unit | null> {
    const query = `
      SELECT u.*, p.name as property_name, p.address as property_address,
             l.id as lease_id, l.status as lease_status, l.start_date as lease_start,
             l.end_date as lease_end, t.id as tenant_id,
             CONCAT(usr.first_name, ' ', usr.last_name) as tenant_name,
             usr.email as tenant_email, usr.phone as tenant_phone
      FROM units u
      LEFT JOIN properties p ON u.property_id = p.id
      LEFT JOIN leases l ON u.id = l.unit_id AND l.status = 'active'
      LEFT JOIN tenants t ON l.tenant_id = t.id
      LEFT JOIN users usr ON t.user_id = usr.id
      WHERE u.id = ?
    `;
    
    const results = await executeQuery(query, [id]);
    
    if (results.length === 0) {
      return null;
    }
    
    const unit = results[0];
    
    // Parse JSON fields
    if (unit.amenities) {
      unit.amenities = JSON.parse(unit.amenities);
    }
    
    if (unit.images) {
      unit.images = JSON.parse(unit.images);
    }
    
    return unit;
  }

  // Update unit
  static async update(id: string, updateData: Partial<Unit>): Promise<Unit | null> {
    const allowedFields = [
      'unit_number', 'unit_type', 'floor_number', 'square_feet', 'bedrooms',
      'bathrooms', 'monthly_rent', 'security_deposit', 'description',
      'amenities', 'images', 'is_occupied', 'is_available'
    ];
    
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key) && updateData[key as keyof Unit] !== undefined) {
        if (key === 'amenities' || key === 'images') {
          updates.push(`${key} = ?`);
          params.push(JSON.stringify(updateData[key as keyof Unit]));
        } else {
          updates.push(`${key} = ?`);
          params.push(updateData[key as keyof Unit]);
        }
      }
    });
    
    if (updates.length === 0) {
      return await this.findById(id);
    }
    
    params.push(id);
    
    const query = `
      UPDATE units 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    await executeQuery(query, params);
    return await this.findById(id);
  }

  // Get units by property ID
  static async findByPropertyId(
    propertyId: string,
    options: PaginationQuery = {}
  ): Promise<{ units: Unit[]; total: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'unit_number',
      sortOrder = 'asc',
      search
    } = options;
    
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE u.property_id = ?';
    const params: any[] = [propertyId];
    
    if (search) {
      whereClause += ' AND (u.unit_number LIKE ? OR u.description LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm);
    }
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM units u ${whereClause}`;
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;
    
    // Get units
    const query = `
      SELECT u.*, p.name as property_name,
             l.id as lease_id, l.status as lease_status,
             CONCAT(usr.first_name, ' ', usr.last_name) as tenant_name
      FROM units u
      LEFT JOIN properties p ON u.property_id = p.id
      LEFT JOIN leases l ON u.id = l.unit_id AND l.status = 'active'
      LEFT JOIN tenants t ON l.tenant_id = t.id
      LEFT JOIN users usr ON t.user_id = usr.id
      ${whereClause}
      ORDER BY u.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const units = await executeQuery(query, params);
    
    // Parse JSON fields
    units.forEach((unit: any) => {
      if (unit.amenities) {
        unit.amenities = JSON.parse(unit.amenities);
      }
      
      if (unit.images) {
        unit.images = JSON.parse(unit.images);
      }
    });
    
    return { units, total };
  }

  // Delete unit
  static async delete(id: string): Promise<boolean> {
    // Get unit to find property_id for updating count
    const unit = await this.findById(id);
    if (!unit) {
      return false;
    }
    
    const query = 'DELETE FROM units WHERE id = ?';
    const result = await executeQuery(query, [id]);
    
    if (result.affectedRows > 0) {
      // Update property total_units count
      await this.updatePropertyUnitCount(unit.property_id);
      return true;
    }
    
    return false;
  }

  // Update property unit count
  static async updatePropertyUnitCount(propertyId: string): Promise<void> {
    const query = `
      UPDATE properties 
      SET total_units = (
        SELECT COUNT(*) FROM units WHERE property_id = ?
      )
      WHERE id = ?
    `;
    
    await executeQuery(query, [propertyId, propertyId]);
  }

  // Get available units
  static async findAvailable(propertyId?: string): Promise<Unit[]> {
    let query = `
      SELECT u.*, p.name as property_name, p.address as property_address
      FROM units u
      LEFT JOIN properties p ON u.property_id = p.id
      WHERE u.is_available = TRUE AND u.is_occupied = FALSE
    `;
    
    const params: any[] = [];
    
    if (propertyId) {
      query += ' AND u.property_id = ?';
      params.push(propertyId);
    }
    
    query += ' ORDER BY p.name, u.unit_number';
    
    const units = await executeQuery(query, params);
    
    // Parse JSON fields
    units.forEach((unit: any) => {
      if (unit.amenities) {
        unit.amenities = JSON.parse(unit.amenities);
      }
      
      if (unit.images) {
        unit.images = JSON.parse(unit.images);
      }
    });
    
    return units;
  }

  // Check if unit number exists in property
  static async unitNumberExists(propertyId: string, unitNumber: string, excludeId?: string): Promise<boolean> {
    let query = 'SELECT COUNT(*) as count FROM units WHERE property_id = ? AND unit_number = ?';
    const params: any[] = [propertyId, unitNumber];
    
    if (excludeId) {
      query += ' AND id != ?';
      params.push(excludeId);
    }
    
    const result = await executeQuery(query, params);
    return result[0].count > 0;
  }
}
