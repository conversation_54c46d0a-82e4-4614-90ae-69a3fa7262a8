import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { pool, executeQuery, executeTransaction } from '../config/database.js';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function checkDatabaseExists() {
  try {
    console.log('🔍 Checking database connection...');
    const connection = await pool.getConnection();
    
    // Check if we can connect to the database
    await connection.query('SELECT 1');
    console.log('✅ Connected to database:', process.env.DB_NAME);
    
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

async function checkTablesExist() {
  try {
    console.log('🔍 Checking if tables exist...');
    
    const tables = await executeQuery('SHOW TABLES');
    const tableNames = tables.map((row: any) => Object.values(row)[0]);
    
    console.log('📋 Found tables:', tableNames);
    
    const requiredTables = [
      'users', 'properties', 'units', 'tenants', 'leases', 
      'invoices', 'payments', 'maintenance_requests', 'audit_logs'
    ];
    
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));
    
    if (missingTables.length > 0) {
      console.log('❌ Missing tables:', missingTables);
      return false;
    }
    
    console.log('✅ All required tables exist');
    return true;
  } catch (error) {
    console.error('❌ Error checking tables:', error);
    return false;
  }
}

async function runMigration() {
  try {
    console.log('🔄 Running database migration...');
    
    // Path to migration file
    const migrationPath = path.join(process.cwd(), '..', '..', 'supabase', 'migrations', '20250710060353_old_dream.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Migration file not found:', migrationPath);
      return false;
    }
    
    // Read migration file
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split SQL by semicolons and clean up
    const statements = migrationSQL
      .replace(/--.*$/gm, '') // Remove comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      try {
        await executeQuery(statement);
        successCount++;
        
        // Log progress for major operations
        if (statement.toLowerCase().includes('create table')) {
          const tableName = statement.match(/create table\s+(\w+)/i)?.[1];
          console.log(`✅ Created table: ${tableName}`);
        }
      } catch (error) {
        errorCount++;
        console.error(`❌ Error in statement ${i + 1}:`, error.message);
        
        // Continue with next statement unless it's a critical error
        if (error.message.includes('already exists')) {
          console.log(`ℹ️  Table/object already exists, continuing...`);
        }
      }
    }
    
    console.log(`📊 Migration completed: ${successCount} successful, ${errorCount} errors`);
    return errorCount === 0 || successCount > 0;
  } catch (error) {
    console.error('❌ Migration failed:', error);
    return false;
  }
}

async function seedInitialData() {
  try {
    console.log('🌱 Seeding initial data...');
    
    // Check if users table has data
    const userCount = await executeQuery('SELECT COUNT(*) as count FROM users');
    
    if (userCount[0].count > 0) {
      console.log('ℹ️  Users already exist, skipping seed data');
      return true;
    }
    
    console.log('🔄 Creating initial users...');
    
    // Create sample users
    const users = [
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Admin',
        last_name: 'User',
        role: 'landlord',
        phone: '+************'
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Property',
        last_name: 'Manager',
        role: 'property_manager',
        phone: '+************'
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Tenant',
        role: 'tenant',
        phone: '+************'
      }
    ];
    
    for (const user of users) {
      const hashedPassword = await bcrypt.hash(user.password, 12);
      
      await executeQuery(`
        INSERT INTO users (
          id, email, password_hash, first_name, last_name, phone, role, 
          is_active, email_verified, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, TRUE, TRUE, NOW(), NOW())
      `, [
        user.id,
        user.email,
        hashedPassword,
        user.first_name,
        user.last_name,
        user.phone,
        user.role
      ]);
      
      console.log(`✅ Created user: ${user.email} (${user.role})`);
    }
    
    // Create sample properties
    console.log('🏢 Creating sample properties...');
    
    const landlordId = users.find(u => u.role === 'landlord')?.id;
    const managerId = users.find(u => u.role === 'property_manager')?.id;
    
    const properties = [
      {
        id: uuidv4(),
        name: 'Sunset Apartments',
        description: 'Modern residential complex with great amenities',
        address: '123 Sunset Boulevard',
        city: 'Nairobi',
        state: 'Nairobi County',
        country: 'Kenya',
        property_type: 'residential',
        landlord_id: landlordId,
        manager_id: managerId
      },
      {
        id: uuidv4(),
        name: 'Downtown Office Complex',
        description: 'Prime commercial space in the city center',
        address: '456 Business District',
        city: 'Nairobi',
        state: 'Nairobi County',
        country: 'Kenya',
        property_type: 'commercial',
        landlord_id: landlordId,
        manager_id: managerId
      }
    ];
    
    for (const property of properties) {
      await executeQuery(`
        INSERT INTO properties (
          id, name, description, address, city, state, country, property_type,
          landlord_id, manager_id, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
      `, [
        property.id,
        property.name,
        property.description,
        property.address,
        property.city,
        property.state,
        property.country,
        property.property_type,
        property.landlord_id,
        property.manager_id
      ]);
      
      console.log(`✅ Created property: ${property.name}`);
      
      // Create sample units for each property
      const unitCount = property.property_type === 'residential' ? 4 : 2;
      
      for (let i = 1; i <= unitCount; i++) {
        const unitId = uuidv4();
        const unitNumber = property.property_type === 'residential' ? `A${i}` : `Office-${i}`;
        const unitType = property.property_type === 'residential' ? '2br' : 'office';
        const monthlyRent = property.property_type === 'residential' ? 25000 + (i * 5000) : 50000 + (i * 10000);
        
        await executeQuery(`
          INSERT INTO units (
            id, property_id, unit_number, unit_type, bedrooms, bathrooms,
            monthly_rent, security_deposit, is_occupied, is_available,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, FALSE, TRUE, NOW(), NOW())
        `, [
          unitId,
          property.id,
          unitNumber,
          unitType,
          property.property_type === 'residential' ? 2 : 0,
          property.property_type === 'residential' ? 2 : 1,
          monthlyRent,
          monthlyRent * 2, // Security deposit = 2 months rent
        ]);
        
        console.log(`✅ Created unit: ${unitNumber} - KES ${monthlyRent.toLocaleString()}/month`);
      }
    }
    
    // Update property total_units count
    await executeQuery(`
      UPDATE properties p 
      SET total_units = (
        SELECT COUNT(*) FROM units u WHERE u.property_id = p.id
      )
    `);
    
    console.log('✅ Initial data seeded successfully');
    return true;
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 PropertyPro Database Setup\n');
  
  try {
    // Check database connection
    const dbConnected = await checkDatabaseExists();
    if (!dbConnected) {
      console.error('❌ Cannot connect to database. Please check your configuration.');
      process.exit(1);
    }
    
    // Check if tables exist
    const tablesExist = await checkTablesExist();
    
    if (!tablesExist) {
      console.log('📋 Tables missing, running migration...');
      const migrationSuccess = await runMigration();
      
      if (!migrationSuccess) {
        console.error('❌ Migration failed. Please check the logs above.');
        process.exit(1);
      }
    } else {
      console.log('✅ All tables exist, skipping migration');
    }
    
    // Seed initial data
    const seedSuccess = await seedInitialData();
    if (!seedSuccess) {
      console.error('❌ Seeding failed, but database structure is ready');
    }
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 You can now login with:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    console.log('   Role: Landlord');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  } finally {
    try {
      await pool.end();
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

// Run the setup
main();
