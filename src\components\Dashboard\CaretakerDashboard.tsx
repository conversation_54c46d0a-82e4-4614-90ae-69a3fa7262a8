import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>riangle, 
  CheckCircle, 
  Clock,
  Calendar,
  MapPin,
  User,
  Phone,
  FileText,
  Camera,
  Tool
} from 'lucide-react';

interface MaintenanceRequest {
  id: number;
  property: string;
  unit: string;
  tenant: string;
  issue: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'completed' | 'on_hold';
  assignedDate: string;
  dueDate: string;
  description: string;
}

const CaretakerDashboard: React.FC = () => {
  const [maintenanceRequests, setMaintenanceRequests] = useState<MaintenanceRequest[]>([]);
  const [todaysTasks, setTodaysTasks] = useState<MaintenanceRequest[]>([]);
  const [stats, setStats] = useState({
    totalRequests: 0,
    completedToday: 0,
    pendingUrgent: 0,
    inProgress: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMaintenanceData();
  }, []);

  const fetchMaintenanceData = async () => {
    try {
      setLoading(true);
      
      // Mock maintenance data (would come from API)
      const mockRequests: MaintenanceRequest[] = [
        {
          id: 1,
          property: 'Sunset Apartments',
          unit: 'A1',
          tenant: 'John Doe',
          issue: 'Leaking faucet in kitchen',
          priority: 'high',
          status: 'open',
          assignedDate: '2024-01-20',
          dueDate: '2024-01-22',
          description: 'Kitchen faucet has been leaking for 2 days. Water damage possible.'
        },
        {
          id: 2,
          property: 'Downtown Office',
          unit: 'Office-101',
          tenant: 'ABC Company',
          issue: 'AC not working',
          priority: 'urgent',
          status: 'in_progress',
          assignedDate: '2024-01-19',
          dueDate: '2024-01-20',
          description: 'Air conditioning unit completely stopped working. Office too hot.'
        },
        {
          id: 3,
          property: 'Garden View',
          unit: 'B2',
          tenant: 'Jane Smith',
          issue: 'Broken window lock',
          priority: 'medium',
          status: 'open',
          assignedDate: '2024-01-21',
          dueDate: '2024-01-25',
          description: 'Window lock mechanism is broken, security concern.'
        },
        {
          id: 4,
          property: 'Sunset Apartments',
          unit: 'A3',
          tenant: 'Mike Johnson',
          issue: 'Light bulb replacement',
          priority: 'low',
          status: 'completed',
          assignedDate: '2024-01-18',
          dueDate: '2024-01-20',
          description: 'Replace burned out light bulbs in living room.'
        }
      ];
      
      setMaintenanceRequests(mockRequests);
      setTodaysTasks(mockRequests.filter(req => req.status === 'open' || req.status === 'in_progress'));
      
      setStats({
        totalRequests: mockRequests.length,
        completedToday: mockRequests.filter(req => req.status === 'completed').length,
        pendingUrgent: mockRequests.filter(req => req.priority === 'urgent' && req.status !== 'completed').length,
        inProgress: mockRequests.filter(req => req.status === 'in_progress').length
      });
      
    } catch (error) {
      console.error('Failed to fetch maintenance data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      default: return 'text-green-600 bg-green-100 border-green-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'on_hold': return 'text-gray-600 bg-gray-100';
      default: return 'text-red-600 bg-red-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle size={16} className="text-green-600" />;
      case 'in_progress': return <Clock size={16} className="text-blue-600" />;
      case 'on_hold': return <Clock size={16} className="text-gray-600" />;
      default: return <AlertTriangle size={16} className="text-red-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const StatCard = ({ title, value, icon: Icon, color }: any) => (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Caretaker Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage maintenance requests and property upkeep.</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Wrench size={16} className="mr-2" />
            Update Status
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <Camera size={16} className="mr-2" />
            Upload Photo
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Requests"
          value={stats.totalRequests}
          icon={Wrench}
          color="bg-blue-500"
        />
        <StatCard
          title="In Progress"
          value={stats.inProgress}
          icon={Clock}
          color="bg-yellow-500"
        />
        <StatCard
          title="Urgent Pending"
          value={stats.pendingUrgent}
          icon={AlertTriangle}
          color="bg-red-500"
        />
        <StatCard
          title="Completed Today"
          value={stats.completedToday}
          icon={CheckCircle}
          color="bg-green-500"
        />
      </div>

      {/* Today's Tasks */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Today's Tasks</h3>
          <Calendar size={20} className="text-blue-600" />
        </div>
        <div className="space-y-4">
          {todaysTasks.map((task) => (
            <div key={task.id} className={`p-4 border rounded-lg ${getPriorityColor(task.priority)}`}>
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  {getStatusIcon(task.status)}
                  <div className="ml-3">
                    <h4 className="font-medium text-gray-900">{task.issue}</h4>
                    <div className="flex items-center text-sm text-gray-600 mt-1">
                      <MapPin size={14} className="mr-1" />
                      <span>{task.property} - {task.unit}</span>
                      <User size={14} className="ml-3 mr-1" />
                      <span>{task.tenant}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(task.priority)}`}>
                    {task.priority}
                  </span>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`}>
                    {task.status.replace('_', ' ')}
                  </span>
                </div>
              </div>
              <p className="text-sm text-gray-700 mb-3">{task.description}</p>
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Due: {formatDate(task.dueDate)}
                </div>
                <div className="flex space-x-2">
                  <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    Start Work
                  </button>
                  <button className="text-green-600 hover:text-green-700 text-sm font-medium">
                    Complete
                  </button>
                  <button className="text-gray-600 hover:text-gray-700 text-sm font-medium">
                    Details
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* All Maintenance Requests */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">All Maintenance Requests</h3>
          <Wrench size={20} className="text-orange-600" />
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-600">Request</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Location</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Priority</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Due Date</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
              </tr>
            </thead>
            <tbody>
              {maintenanceRequests.map((request) => (
                <tr key={request.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4">
                    <div>
                      <p className="font-medium text-gray-900">{request.issue}</p>
                      <p className="text-sm text-gray-600">{request.tenant}</p>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div>
                      <p className="text-sm text-gray-900">{request.property}</p>
                      <p className="text-sm text-gray-600">{request.unit}</p>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(request.priority)}`}>
                      {request.priority}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center">
                      {getStatusIcon(request.status)}
                      <span className="ml-2 text-sm capitalize">{request.status.replace('_', ' ')}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-600">
                    {formatDate(request.dueDate)}
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex space-x-2">
                      <button className="text-blue-600 hover:text-blue-700 text-sm">
                        View
                      </button>
                      <button className="text-green-600 hover:text-green-700 text-sm">
                        Update
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
            <Wrench size={20} className="text-blue-600 mx-auto mb-2" />
            <p className="font-medium text-gray-900">Update Status</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
            <Camera size={20} className="text-green-600 mx-auto mb-2" />
            <p className="font-medium text-gray-900">Upload Photo</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
            <Phone size={20} className="text-purple-600 mx-auto mb-2" />
            <p className="font-medium text-gray-900">Call Tenant</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
            <FileText size={20} className="text-orange-600 mx-auto mb-2" />
            <p className="font-medium text-gray-900">Create Report</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default CaretakerDashboard;
