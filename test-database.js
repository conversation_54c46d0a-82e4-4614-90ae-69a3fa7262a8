// Simple database test script
import mysql from 'mysql2/promise';

async function testDatabase() {
  console.log('🔍 Testing PropertyPro Database Setup...\n');
  
  const config = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '',
    database: 'property_management'
  };
  
  try {
    // Connect to database
    console.log('🔄 Connecting to database...');
    const connection = await mysql.createConnection(config);
    console.log('✅ Database connection successful\n');
    
    // Test tables exist
    console.log('📋 Checking tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);
    
    const requiredTables = ['users', 'properties', 'units'];
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));
    
    if (missingTables.length > 0) {
      console.log('❌ Missing tables:', missingTables);
      console.log('   Please run the setup-database.sql script');
      return;
    }
    
    console.log('✅ All required tables exist:', tableNames.join(', '));
    
    // Test data exists
    console.log('\n📊 Checking sample data...');
    
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [properties] = await connection.execute('SELECT COUNT(*) as count FROM properties');
    const [units] = await connection.execute('SELECT COUNT(*) as count FROM units');
    
    console.log(`✅ Users: ${users[0].count}`);
    console.log(`✅ Properties: ${properties[0].count}`);
    console.log(`✅ Units: ${units[0].count}`);
    
    if (users[0].count === 0) {
      console.log('\n⚠️  No sample data found. Please run setup-database.sql');
      return;
    }
    
    // Test login credentials
    console.log('\n🔐 Testing login credentials...');
    const [loginUsers] = await connection.execute(`
      SELECT email, role, first_name, last_name 
      FROM users 
      WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
    `);
    
    console.log('✅ Available login accounts:');
    loginUsers.forEach(user => {
      console.log(`   📧 ${user.email} (${user.role}) - ${user.first_name} ${user.last_name}`);
    });
    
    // Test property statistics
    console.log('\n🏢 Property Statistics:');
    const [propertyStats] = await connection.execute(`
      SELECT 
        p.name,
        p.property_type,
        COUNT(u.id) as total_units,
        SUM(CASE WHEN u.is_occupied = TRUE THEN 1 ELSE 0 END) as occupied_units,
        SUM(u.monthly_rent) as potential_revenue,
        SUM(CASE WHEN u.is_occupied = TRUE THEN u.monthly_rent ELSE 0 END) as actual_revenue
      FROM properties p
      LEFT JOIN units u ON p.id = u.property_id
      GROUP BY p.id, p.name, p.property_type
    `);
    
    propertyStats.forEach(prop => {
      const occupancyRate = prop.total_units > 0 ? 
        Math.round((prop.occupied_units / prop.total_units) * 100) : 0;
      
      console.log(`   🏠 ${prop.name} (${prop.property_type})`);
      console.log(`      Units: ${prop.occupied_units}/${prop.total_units} (${occupancyRate}% occupied)`);
      console.log(`      Revenue: KES ${prop.actual_revenue?.toLocaleString() || 0} / KES ${prop.potential_revenue?.toLocaleString() || 0}`);
    });
    
    await connection.end();
    
    console.log('\n🎉 Database test completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Start backend: cd server && npm run dev');
    console.log('   2. Start frontend: npm run dev');
    console.log('   3. Login with: <EMAIL> / password123');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    
    if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Solution: Create the database first:');
      console.log('   1. Open phpMyAdmin (http://localhost/phpmyadmin)');
      console.log('   2. Create database: property_management');
      console.log('   3. Import: setup-database.sql');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solution: Start MySQL server:');
      console.log('   1. Open XAMPP Control Panel');
      console.log('   2. Start MySQL service');
    }
  }
}

testDatabase();
