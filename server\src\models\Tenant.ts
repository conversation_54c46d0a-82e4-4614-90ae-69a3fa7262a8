import { executeQuery, executeTransaction } from '../config/database.js';
import { Tenant, PaginationQuery } from '../types/index.js';
import { v4 as uuidv4 } from 'uuid';

export class TenantModel {
  // Create a new tenant
  static async create(tenantData: Partial<Tenant>): Promise<Tenant> {
    const id = uuidv4();
    
    const query = `
      INSERT INTO tenants (
        id, user_id, emergency_contact_name, emergency_contact_phone, 
        emergency_contact_relationship, employer, monthly_income, id_number,
        id_document_url, background_check_status, move_in_date, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      id,
      tenantData.user_id,
      tenantData.emergency_contact_name || null,
      tenantData.emergency_contact_phone || null,
      tenantData.emergency_contact_relationship || null,
      tenantData.employer || null,
      tenantData.monthly_income || null,
      tenantData.id_number || null,
      tenantData.id_document_url || null,
      tenantData.background_check_status || 'pending',
      tenantData.move_in_date || null,
      tenantData.notes || null
    ];
    
    await executeQuery(query, params);
    
    return await this.findById(id) as Tenant;
  }

  // Find tenant by ID
  static async findById(id: string): Promise<Tenant | null> {
    const query = `
      SELECT t.*, 
             CONCAT(u.first_name, ' ', u.last_name) as full_name,
             u.email, u.phone as user_phone,
             l.id as current_lease_id, l.unit_id as current_unit_id,
             l.start_date as lease_start, l.end_date as lease_end,
             l.monthly_rent as current_rent, l.status as lease_status,
             p.name as property_name, un.unit_number
      FROM tenants t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN leases l ON t.id = l.tenant_id AND l.status = 'active'
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      WHERE t.id = ?
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  // Find tenant by user ID
  static async findByUserId(userId: string): Promise<Tenant | null> {
    const query = `
      SELECT t.*, 
             CONCAT(u.first_name, ' ', u.last_name) as full_name,
             u.email, u.phone as user_phone
      FROM tenants t
      LEFT JOIN users u ON t.user_id = u.id
      WHERE t.user_id = ?
    `;
    
    const results = await executeQuery(query, [userId]);
    return results.length > 0 ? results[0] : null;
  }

  // Update tenant
  static async update(id: string, updateData: Partial<Tenant>): Promise<Tenant | null> {
    const allowedFields = [
      'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',
      'employer', 'monthly_income', 'id_number', 'id_document_url', 
      'background_check_status', 'move_in_date', 'move_out_date', 'notes'
    ];
    
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key) && updateData[key as keyof Tenant] !== undefined) {
        updates.push(`${key} = ?`);
        params.push(updateData[key as keyof Tenant]);
      }
    });
    
    if (updates.length === 0) {
      return await this.findById(id);
    }
    
    params.push(id);
    
    const query = `
      UPDATE tenants 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    await executeQuery(query, params);
    return await this.findById(id);
  }

  // Get all tenants with pagination and filtering
  static async findAll(
    options: PaginationQuery & {
      landlordId?: string;
      propertyId?: string;
      leaseStatus?: string;
      backgroundCheckStatus?: string;
    } = {}
  ): Promise<{ tenants: Tenant[]; total: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      landlordId,
      propertyId,
      leaseStatus,
      backgroundCheckStatus
    } = options;
    
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ` AND p.landlord_id = ?`;
      params.push(landlordId);
    }
    
    if (propertyId) {
      whereClause += ` AND un.property_id = ?`;
      params.push(propertyId);
    }
    
    if (leaseStatus) {
      whereClause += ` AND l.status = ?`;
      params.push(leaseStatus);
    }
    
    if (backgroundCheckStatus) {
      whereClause += ` AND t.background_check_status = ?`;
      params.push(backgroundCheckStatus);
    }
    
    if (search) {
      whereClause += ` AND (CONCAT(u.first_name, ' ', u.last_name) LIKE ? OR u.email LIKE ? OR t.id_number LIKE ?)`;
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }
    
    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT t.id) as total 
      FROM tenants t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN leases l ON t.id = l.tenant_id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      ${whereClause}
    `;
    
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;
    
    // Get tenants
    const query = `
      SELECT DISTINCT t.*, 
             CONCAT(u.first_name, ' ', u.last_name) as full_name,
             u.email, u.phone as user_phone,
             l.id as current_lease_id, l.unit_id as current_unit_id,
             l.start_date as lease_start, l.end_date as lease_end,
             l.monthly_rent as current_rent, l.status as lease_status,
             p.name as property_name, un.unit_number
      FROM tenants t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN leases l ON t.id = l.tenant_id AND l.status = 'active'
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      ${whereClause}
      ORDER BY t.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const tenants = await executeQuery(query, params);
    
    return { tenants, total };
  }

  // Delete tenant (soft delete by setting move_out_date)
  static async delete(id: string): Promise<boolean> {
    const query = `
      UPDATE tenants 
      SET move_out_date = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // Get tenant statistics
  static async getStats(landlordId?: string): Promise<any> {
    let whereClause = '';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause = 'WHERE p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        COUNT(DISTINCT t.id) as total_tenants,
        COUNT(CASE WHEN l.status = 'active' THEN 1 END) as active_tenants,
        COUNT(CASE WHEN t.background_check_status = 'pending' THEN 1 END) as pending_background_checks,
        COUNT(CASE WHEN t.background_check_status = 'approved' THEN 1 END) as approved_tenants,
        COUNT(CASE WHEN t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_tenants_this_month,
        COUNT(CASE WHEN l.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY) THEN 1 END) as leases_expiring_soon,
        AVG(t.monthly_income) as average_income
      FROM tenants t
      LEFT JOIN leases l ON t.id = l.tenant_id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      ${whereClause}
    `;
    
    const results = await executeQuery(query, params);
    return results[0];
  }

  // Get tenant payment history
  static async getPaymentHistory(tenantId: string): Promise<any[]> {
    const query = `
      SELECT p.*, i.invoice_number, i.amount as invoice_amount, i.due_date,
             l.monthly_rent, un.unit_number, pr.name as property_name
      FROM payments p
      JOIN invoices i ON p.invoice_id = i.id
      JOIN leases l ON i.lease_id = l.id
      JOIN units un ON l.unit_id = un.id
      JOIN properties pr ON un.property_id = pr.id
      WHERE p.tenant_id = ?
      ORDER BY p.payment_date DESC
    `;
    
    return await executeQuery(query, [tenantId]);
  }

  // Get tenant maintenance requests
  static async getMaintenanceRequests(tenantId: string): Promise<any[]> {
    const query = `
      SELECT mr.*, un.unit_number, p.name as property_name
      FROM maintenance_requests mr
      JOIN units un ON mr.unit_id = un.id
      JOIN properties p ON un.property_id = p.id
      WHERE mr.tenant_id = ?
      ORDER BY mr.created_at DESC
    `;

    return await executeQuery(query, [tenantId]);
  }

  // Get tenant active leases
  static async getActiveLeases(tenantId: string): Promise<any[]> {
    const query = `
      SELECT l.*, un.unit_number, p.name as property_name
      FROM leases l
      JOIN units un ON l.unit_id = un.id
      JOIN properties p ON un.property_id = p.id
      WHERE l.tenant_id = ? AND l.status = 'active'
      ORDER BY l.start_date DESC
    `;

    return await executeQuery(query, [tenantId]);
  }
}
