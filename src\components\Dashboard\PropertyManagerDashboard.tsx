import React, { useState, useEffect } from 'react';
import { 
  Building, 
  Users, 
  Wrench, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Phone,
  MapPin
} from 'lucide-react';
import { dashboardAPI, propertiesAPI } from '../../services/api';

interface DashboardStats {
  totalProperties: number;
  totalUnits: number;
  occupiedUnits: number;
  vacantUnits: number;
  occupancyRate: number;
  maintenanceRequests: number;
  urgentMaintenance: number;
  completedMaintenance: number;
  totalTenants: number;
  leasesExpiringThisMonth: number;
}

const PropertyManagerDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [managedProperties, setManagedProperties] = useState<any[]>([]);
  const [recentMaintenance, setRecentMaintenance] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch dashboard stats
      const dashboardStats = await dashboardAPI.getStats();
      setStats(dashboardStats);
      
      // Fetch managed properties
      const propertiesResponse = await propertiesAPI.getAll({ limit: 10 });
      setManagedProperties(propertiesResponse.data || []);
      
      // Mock recent maintenance requests
      setRecentMaintenance([
        { id: 1, property: 'Sunset Apartments', unit: 'A1', issue: 'Leaking faucet', priority: 'medium', status: 'open' },
        { id: 2, property: 'Downtown Office', unit: 'Office-101', issue: 'AC not working', priority: 'high', status: 'in_progress' },
        { id: 3, property: 'Garden View', unit: 'B2', issue: 'Broken window', priority: 'urgent', status: 'open' },
      ]);
      
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const StatCard = ({ title, value, icon: Icon, color, subtitle }: any) => (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-green-600 bg-green-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle size={16} className="text-green-600" />;
      case 'in_progress': return <Clock size={16} className="text-blue-600" />;
      default: return <AlertTriangle size={16} className="text-red-600" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Property Manager Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage your assigned properties and tenant relations.</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Wrench size={16} className="mr-2" />
            New Maintenance
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <Users size={16} className="mr-2" />
            Add Tenant
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Managed Properties"
          value={stats?.totalProperties || 0}
          icon={Building}
          color="bg-blue-500"
          subtitle="Under your management"
        />
        <StatCard
          title="Total Units"
          value={stats?.totalUnits || 0}
          icon={MapPin}
          color="bg-green-500"
          subtitle={`${stats?.occupiedUnits || 0} occupied`}
        />
        <StatCard
          title="Active Tenants"
          value={stats?.totalTenants || 0}
          icon={Users}
          color="bg-purple-500"
          subtitle="Current residents"
        />
        <StatCard
          title="Maintenance Requests"
          value={stats?.maintenanceRequests || 0}
          icon={Wrench}
          color="bg-orange-500"
          subtitle={`${stats?.urgentMaintenance || 0} urgent`}
        />
      </div>

      {/* Property Overview and Maintenance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Property Overview */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Property Overview</h3>
            <Building size={20} className="text-blue-500" />
          </div>
          <div className="space-y-4">
            {managedProperties.slice(0, 5).map((property) => (
              <div key={property.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <Building size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{property.name}</p>
                    <p className="text-sm text-gray-600">{property.city}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    {property.occupied_units || 0}/{property.total_units || 0}
                  </p>
                  <p className="text-xs text-gray-500">
                    {property.total_units > 0 
                      ? Math.round(((property.occupied_units || 0) / property.total_units) * 100)
                      : 0}% occupied
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Maintenance Requests */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Recent Maintenance</h3>
            <Wrench size={20} className="text-orange-500" />
          </div>
          <div className="space-y-3">
            {recentMaintenance.map((request) => (
              <div key={request.id} className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    {getStatusIcon(request.status)}
                    <span className="ml-2 font-medium text-gray-900">{request.property}</span>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(request.priority)}`}>
                    {request.priority}
                  </span>
                </div>
                <p className="text-sm text-gray-600">{request.unit}: {request.issue}</p>
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-gray-500">Status: {request.status}</span>
                  <button className="text-blue-600 hover:text-blue-700 text-xs font-medium">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Tasks and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Tasks */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Tasks</h3>
          <div className="space-y-3">
            <div className="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <Calendar size={20} className="text-yellow-600 mr-3" />
              <div>
                <p className="font-medium text-yellow-800">Property Inspection</p>
                <p className="text-sm text-yellow-600">Sunset Apartments - Unit A3</p>
              </div>
            </div>
            <div className="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <Phone size={20} className="text-blue-600 mr-3" />
              <div>
                <p className="font-medium text-blue-800">Tenant Follow-up</p>
                <p className="text-sm text-blue-600">Lease renewal discussion</p>
              </div>
            </div>
            <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle size={20} className="text-green-600 mr-3" />
              <div>
                <p className="font-medium text-green-800">Maintenance Review</p>
                <p className="text-sm text-green-600">Approve completed work orders</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <Wrench size={20} className="text-orange-600 mb-2" />
              <p className="font-medium text-gray-900">Log Maintenance</p>
              <p className="text-sm text-gray-600">Create work order</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <Users size={20} className="text-blue-600 mb-2" />
              <p className="font-medium text-gray-900">Contact Tenant</p>
              <p className="text-sm text-gray-600">Send message</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <Calendar size={20} className="text-green-600 mb-2" />
              <p className="font-medium text-gray-900">Schedule Visit</p>
              <p className="text-sm text-gray-600">Property inspection</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <FileText size={20} className="text-purple-600 mb-2" />
              <p className="font-medium text-gray-900">Generate Report</p>
              <p className="text-sm text-gray-600">Property status</p>
            </button>
          </div>
        </div>
      </div>

      {/* Alerts */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Priority Alerts</h3>
        <div className="space-y-3">
          {stats?.leasesExpiringThisMonth > 0 && (
            <div className="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <Calendar size={20} className="text-yellow-600 mr-3" />
              <div>
                <p className="font-medium text-yellow-800">
                  {stats.leasesExpiringThisMonth} lease(s) expiring this month
                </p>
                <p className="text-sm text-yellow-600">Contact tenants for renewal discussions</p>
              </div>
            </div>
          )}
          {stats?.urgentMaintenance > 0 && (
            <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertTriangle size={20} className="text-red-600 mr-3" />
              <div>
                <p className="font-medium text-red-800">
                  {stats.urgentMaintenance} urgent maintenance request(s)
                </p>
                <p className="text-sm text-red-600">Immediate attention required</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PropertyManagerDashboard;
