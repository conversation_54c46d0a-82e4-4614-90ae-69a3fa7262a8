import { executeQuery, executeTransaction } from '../config/database.js';
import { Property, CreatePropertyRequest, PaginationQuery } from '../types/index.js';
import { v4 as uuidv4 } from 'uuid';

export class PropertyModel {
  // Create a new property
  static async create(propertyData: CreatePropertyRequest, landlordId: string): Promise<Property> {
    const id = uuidv4();
    
    const query = `
      INSERT INTO properties (
        id, name, description, address, city, state, postal_code, country,
        property_type, landlord_id, manager_id, images, amenities, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE)
    `;
    
    const params = [
      id,
      propertyData.name,
      propertyData.description || null,
      propertyData.address,
      propertyData.city,
      propertyData.state || null,
      propertyData.postal_code || null,
      propertyData.country || 'Kenya',
      propertyData.property_type,
      landlordId,
      propertyData.manager_id || null,
      propertyData.images ? JSON.stringify(propertyData.images) : null,
      propertyData.amenities ? JSON.stringify(propertyData.amenities) : null
    ];
    
    await executeQuery(query, params);
    
    // Return the created property
    return await this.findById(id) as Property;
  }

  // Find property by ID
  static async findById(id: string): Promise<Property | null> {
    const query = `
      SELECT p.*,
             CONCAT(u1.first_name, ' ', u1.last_name) as landlord_name,
             CONCAT(u2.first_name, ' ', u2.last_name) as manager_name,
             COALESCE((SELECT COUNT(*) FROM units WHERE property_id = p.id), 0) as total_units,
             COALESCE((SELECT COUNT(*) FROM units WHERE property_id = p.id AND is_occupied = TRUE), 0) as occupied_units
      FROM properties p
      LEFT JOIN users u1 ON p.landlord_id = u1.id
      LEFT JOIN users u2 ON p.manager_id = u2.id
      WHERE p.id = ? AND p.is_active = TRUE
    `;
    
    const results = await executeQuery(query, [id]);
    
    if (results.length === 0) {
      return null;
    }
    
    const property = results[0];
    
    // Parse JSON fields
    if (property.images) {
      property.images = JSON.parse(property.images);
    }
    
    if (property.amenities) {
      property.amenities = JSON.parse(property.amenities);
    }
    
    return property;
  }

  // Update property
  static async update(id: string, updateData: Partial<Property>): Promise<Property | null> {
    const allowedFields = [
      'name', 'description', 'address', 'city', 'state', 'postal_code', 
      'country', 'property_type', 'manager_id', 'images', 'amenities', 'is_active'
    ];
    
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key) && updateData[key as keyof Property] !== undefined) {
        if (key === 'images' || key === 'amenities') {
          updates.push(`${key} = ?`);
          params.push(JSON.stringify(updateData[key as keyof Property]));
        } else {
          updates.push(`${key} = ?`);
          params.push(updateData[key as keyof Property]);
        }
      }
    });
    
    if (updates.length === 0) {
      return await this.findById(id);
    }
    
    params.push(id);
    
    const query = `
      UPDATE properties 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_active = TRUE
    `;
    
    await executeQuery(query, params);
    return await this.findById(id);
  }

  // Get all properties with pagination and filtering
  static async findAll(
    options: PaginationQuery & {
      landlordId?: string;
      managerId?: string;
      propertyType?: string;
      city?: string;
    } = {}
  ): Promise<{ properties: Property[]; total: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      landlordId,
      managerId,
      propertyType,
      city
    } = options;
    
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE p.is_active = TRUE';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ' AND p.landlord_id = ?';
      params.push(landlordId);
    }
    
    if (managerId) {
      whereClause += ' AND p.manager_id = ?';
      params.push(managerId);
    }
    
    if (propertyType) {
      whereClause += ' AND p.property_type = ?';
      params.push(propertyType);
    }
    
    if (city) {
      whereClause += ' AND p.city = ?';
      params.push(city);
    }
    
    if (search) {
      whereClause += ' AND (p.name LIKE ? OR p.address LIKE ? OR p.city LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }
    
    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM properties p 
      ${whereClause}
    `;
    
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;
    
    // Get properties
    const query = `
      SELECT p.*,
             CONCAT(u1.first_name, ' ', u1.last_name) as landlord_name,
             CONCAT(u2.first_name, ' ', u2.last_name) as manager_name,
             COALESCE((SELECT COUNT(*) FROM units WHERE property_id = p.id), 0) as total_units,
             COALESCE((SELECT COUNT(*) FROM units WHERE property_id = p.id AND is_occupied = TRUE), 0) as occupied_units
      FROM properties p
      LEFT JOIN users u1 ON p.landlord_id = u1.id
      LEFT JOIN users u2 ON p.manager_id = u2.id
      ${whereClause}
      ORDER BY p.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const properties = await executeQuery(query, params);
    
    // Parse JSON fields
    properties.forEach((property: any) => {
      if (property.images) {
        property.images = JSON.parse(property.images);
      }
      
      if (property.amenities) {
        property.amenities = JSON.parse(property.amenities);
      }
    });
    
    return { properties, total };
  }

  // Soft delete property
  static async delete(id: string): Promise<boolean> {
    const query = `
      UPDATE properties 
      SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // Get property statistics
  static async getStats(landlordId?: string): Promise<any> {
    let whereClause = '';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause = 'WHERE p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        COUNT(DISTINCT p.id) as total_properties,
        COUNT(u.id) as total_units,
        SUM(CASE WHEN u.is_occupied = TRUE THEN 1 ELSE 0 END) as occupied_units,
        SUM(CASE WHEN u.is_occupied = FALSE THEN 1 ELSE 0 END) as vacant_units,
        ROUND(SUM(CASE WHEN u.is_occupied = TRUE THEN 1 ELSE 0 END) / COUNT(u.id) * 100, 2) as occupancy_rate,
        SUM(u.monthly_rent) as potential_revenue,
        SUM(CASE WHEN u.is_occupied = TRUE THEN u.monthly_rent ELSE 0 END) as actual_revenue
      FROM properties p
      LEFT JOIN units u ON p.id = u.property_id
      ${whereClause}
    `;
    
    const results = await executeQuery(query, params);
    return results[0];
  }
}
