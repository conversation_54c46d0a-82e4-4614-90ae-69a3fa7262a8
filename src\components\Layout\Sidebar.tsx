import React, { useEffect } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Building,
  Users,
  FileText,
  CreditCard,
  Wrench,
  BarChart3,
  UserCheck,
  Calendar,
  DollarSign,
  X
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar = ({ isOpen, onClose }: SidebarProps) => {
  const { user } = useAuth();
  const location = useLocation();

  // Close sidebar on route change on mobile
  useEffect(() => {
    if (isOpen && window.innerWidth < 1024) {
      onClose();
    }
  }, [location.pathname, isOpen, onClose]);

  const getNavigationItems = () => {
    const baseItems = [
      { to: '/dashboard', icon: LayoutDashboard, label: 'Dashboard' }
    ];

    switch (user?.role) {
      case 'landlord':
      case 'property_manager':
        return [
          ...baseItems,
          { to: '/properties', icon: Building, label: 'Properties' },
          { to: '/tenants', icon: Users, label: 'Tenants' },
          { to: '/leases', icon: FileText, label: 'Leases' },
          { to: '/invoices', icon: CreditCard, label: 'Invoices' },
          { to: '/payments', icon: DollarSign, label: 'Payments' },
          { to: '/maintenance', icon: Wrench, label: 'Maintenance' },
          { to: '/reports', icon: BarChart3, label: 'Reports' }
        ];
      
      case 'caretaker':
        return [
          ...baseItems,
          { to: '/maintenance', icon: Wrench, label: 'Maintenance' },
          { to: '/properties', icon: Building, label: 'Properties' }
        ];
      
      case 'tenant':
        return [
          ...baseItems,
          { to: '/my-invoices', icon: CreditCard, label: 'My Invoices' },
          { to: '/my-payments', icon: DollarSign, label: 'My Payments' },
          { to: '/my-maintenance', icon: Wrench, label: 'Maintenance Requests' },
          { to: '/my-lease', icon: FileText, label: 'My Lease' }
        ];
      
      default:
        return baseItems;
    }
  };

  const navigationItems = getNavigationItems();

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-gray-900 text-white transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        lg:min-h-screen
      `}>
        <div className="p-4 sm:p-6">
          {/* Mobile close button */}
          <div className="lg:hidden flex justify-end mb-4">
            <button
              onClick={onClose}
              className="p-2 text-gray-300 hover:text-white transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          <nav className="space-y-1 sm:space-y-2">
            {navigationItems.map((item) => (
              <NavLink
                key={item.to}
                to={item.to}
                className={({ isActive }) =>
                  `flex items-center space-x-3 px-3 sm:px-4 py-2 sm:py-3 rounded-lg transition-colors ${
                    isActive
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                  }`
                }
              >
                <item.icon size={18} className="sm:w-5 sm:h-5 flex-shrink-0" />
                <span className="text-sm sm:text-base truncate">{item.label}</span>
              </NavLink>
            ))}
          </nav>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;