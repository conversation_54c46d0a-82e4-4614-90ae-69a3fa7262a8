import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Building, 
  Users, 
  FileText, 
  CreditCard, 
  Wrench, 
  BarChart3,
  UserCheck,
  Calendar,
  DollarSign
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

const Sidebar = () => {
  const { user } = useAuth();

  const getNavigationItems = () => {
    const baseItems = [
      { to: '/dashboard', icon: LayoutDashboard, label: 'Dashboard' }
    ];

    switch (user?.role) {
      case 'landlord':
      case 'property_manager':
        return [
          ...baseItems,
          { to: '/properties', icon: Building, label: 'Properties' },
          { to: '/tenants', icon: Users, label: 'Tenants' },
          { to: '/leases', icon: FileText, label: 'Leases' },
          { to: '/invoices', icon: CreditCard, label: 'Invoices' },
          { to: '/payments', icon: DollarSign, label: 'Payments' },
          { to: '/maintenance', icon: Wrench, label: 'Maintenance' },
          { to: '/reports', icon: BarChart3, label: 'Reports' }
        ];
      
      case 'caretaker':
        return [
          ...baseItems,
          { to: '/maintenance', icon: Wrench, label: 'Maintenance' },
          { to: '/properties', icon: Building, label: 'Properties' }
        ];
      
      case 'tenant':
        return [
          ...baseItems,
          { to: '/my-invoices', icon: CreditCard, label: 'My Invoices' },
          { to: '/my-payments', icon: DollarSign, label: 'My Payments' },
          { to: '/my-maintenance', icon: Wrench, label: 'Maintenance Requests' },
          { to: '/my-lease', icon: FileText, label: 'My Lease' }
        ];
      
      default:
        return baseItems;
    }
  };

  const navigationItems = getNavigationItems();

  return (
    <aside className="w-64 bg-gray-900 text-white min-h-screen">
      <div className="p-6">
        <nav className="space-y-2">
          {navigationItems.map((item) => (
            <NavLink
              key={item.to}
              to={item.to}
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                }`
              }
            >
              <item.icon size={20} />
              <span>{item.label}</span>
            </NavLink>
          ))}
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;