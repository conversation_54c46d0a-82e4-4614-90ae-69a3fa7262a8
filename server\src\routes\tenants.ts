import { Router, Request, Response } from 'express';
import { TenantModel } from '../models/Tenant.js';
import { UserModel } from '../models/User.js';
import { authorizeRoles } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { Tenant } from '../types/index.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for document uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(process.cwd(), 'uploads', 'documents');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `tenant-doc-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit for documents
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype) || file.mimetype === 'application/pdf' ||
                     file.mimetype === 'application/msword' ||
                     file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image and document files are allowed'));
    }
  }
});

// Get all tenants
router.get('/',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      propertyId,
      leaseStatus,
      backgroundCheckStatus
    } = req.query as any;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      propertyId,
      leaseStatus,
      backgroundCheckStatus
    };

    // Filter based on user role
    if (req.user!.role === 'landlord') {
      options.landlordId = req.user!.id;
    }

    const { tenants, total } = await TenantModel.findAll(options);

    res.json({
      success: true,
      data: tenants,
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages: Math.ceil(total / options.limit)
      }
    });
  })
);

// Get tenant by ID
router.get('/:id',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const tenant = await TenantModel.findById(id);

    if (!tenant) {
      throw new AppError('Tenant not found', 404);
    }

    // Check permissions
    if (req.user!.role === 'tenant') {
      const userTenant = await TenantModel.findByUserId(req.user!.id);
      if (!userTenant || userTenant.id !== id) {
        throw new AppError('Access denied', 403);
      }
    }

    res.json({
      success: true,
      data: tenant
    });
  })
);

// Create new tenant
router.post('/',
  authorizeRoles('admin', 'landlord', 'property_manager'),
  upload.single('id_document'),
  validate(schemas.createTenant),
  asyncHandler(async (req: Request, res: Response) => {
    const tenantData: Partial<Tenant> = req.body;

    // Handle uploaded document
    const file = req.file;
    if (file) {
      tenantData.id_document_url = `/uploads/documents/${file.filename}`;
    }

    // Check if user exists
    if (tenantData.user_id) {
      const user = await UserModel.findById(tenantData.user_id);
      if (!user) {
        throw new AppError('User not found', 404);
      }

      if (user.role !== 'tenant') {
        throw new AppError('User must have tenant role', 400);
      }

      // Check if tenant already exists for this user
      const existingTenant = await TenantModel.findByUserId(tenantData.user_id);
      if (existingTenant) {
        throw new AppError('Tenant profile already exists for this user', 409);
      }
    }

    const tenant = await TenantModel.create(tenantData);

    res.status(201).json({
      success: true,
      message: 'Tenant created successfully',
      data: tenant
    });
  })
);

// Update tenant
router.put('/:id',
  authorizeRoles('admin', 'landlord', 'property_manager', 'tenant'),
  upload.single('id_document'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData: Partial<Tenant> = req.body;

    // Check if tenant exists
    const existingTenant = await TenantModel.findById(id);
    if (!existingTenant) {
      throw new AppError('Tenant not found', 404);
    }

    // Check permissions
    if (req.user!.role === 'tenant') {
      const userTenant = await TenantModel.findByUserId(req.user!.id);
      if (!userTenant || userTenant.id !== id) {
        throw new AppError('Access denied', 403);
      }

      // Tenants can only update certain fields
      const allowedFields = ['emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship', 'employer', 'monthly_income'];
      Object.keys(updateData).forEach(key => {
        if (!allowedFields.includes(key)) {
          delete updateData[key as keyof Tenant];
        }
      });
    }

    // Handle uploaded document
    const file = req.file;
    if (file) {
      updateData.id_document_url = `/uploads/documents/${file.filename}`;
    }

    const updatedTenant = await TenantModel.update(id, updateData);

    res.json({
      success: true,
      message: 'Tenant updated successfully',
      data: updatedTenant
    });
  })
);

// Delete tenant (soft delete)
router.delete('/:id',
  authorizeRoles('admin', 'landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    // Check if tenant exists
    const tenant = await TenantModel.findById(id);
    if (!tenant) {
      throw new AppError('Tenant not found', 404);
    }

    // Check if tenant has active leases
    const activeLeases = await TenantModel.getActiveLeases(id);
    if (activeLeases && activeLeases.length > 0) {
      throw new AppError('Cannot delete tenant with active leases. Please terminate leases first.', 400);
    }

    const deleted = await TenantModel.delete(id);

    if (!deleted) {
      throw new AppError('Failed to delete tenant', 500);
    }

    res.json({
      success: true,
      message: 'Tenant deleted successfully'
    });
  })
);

// Get tenant payment history
router.get('/:id/payments',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    // Check if tenant exists
    const tenant = await TenantModel.findById(id);
    if (!tenant) {
      throw new AppError('Tenant not found', 404);
    }

    // Check permissions
    if (req.user!.role === 'tenant') {
      const userTenant = await TenantModel.findByUserId(req.user!.id);
      if (!userTenant || userTenant.id !== id) {
        throw new AppError('Access denied', 403);
      }
    }

    const payments = await TenantModel.getPaymentHistory(id);

    res.json({
      success: true,
      data: payments
    });
  })
);

// Get tenant maintenance requests
router.get('/:id/maintenance',
  authorizeRoles('landlord', 'property_manager', 'tenant'),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    // Check if tenant exists
    const tenant = await TenantModel.findById(id);
    if (!tenant) {
      throw new AppError('Tenant not found', 404);
    }

    // Check permissions
    if (req.user!.role === 'tenant') {
      const userTenant = await TenantModel.findByUserId(req.user!.id);
      if (!userTenant || userTenant.id !== id) {
        throw new AppError('Access denied', 403);
      }
    }

    const maintenanceRequests = await TenantModel.getMaintenanceRequests(id);

    res.json({
      success: true,
      data: maintenanceRequests
    });
  })
);

export default router;
