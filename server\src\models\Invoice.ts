import { executeQuery, executeTransaction } from '../config/database.js';
import { Invoice, PaginationQuery } from '../types/index.js';
import { v4 as uuidv4 } from 'uuid';

export class InvoiceModel {
  // Create a new invoice
  static async create(invoiceData: Partial<Invoice>): Promise<Invoice> {
    const id = uuidv4();
    const invoiceNumber = await this.generateInvoiceNumber();
    
    const query = `
      INSERT INTO invoices (
        id, invoice_number, lease_id, tenant_id, invoice_type, amount, tax_amount,
        total_amount, due_date, issue_date, description, line_items, status,
        late_fee_applied, discount_amount, payment_terms, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const totalAmount = (invoiceData.amount || 0) + (invoiceData.tax_amount || 0) - (invoiceData.discount_amount || 0);
    
    const params = [
      id,
      invoiceNumber,
      invoiceData.lease_id,
      invoiceData.tenant_id,
      invoiceData.invoice_type || 'rent',
      invoiceData.amount,
      invoiceData.tax_amount || 0,
      totalAmount,
      invoiceData.due_date,
      invoiceData.issue_date || new Date(),
      invoiceData.description || null,
      invoiceData.line_items ? JSON.stringify(invoiceData.line_items) : null,
      invoiceData.status || 'draft',
      invoiceData.late_fee_applied || false,
      invoiceData.discount_amount || 0,
      invoiceData.payment_terms || 'Net 30',
      invoiceData.notes || null
    ];
    
    await executeQuery(query, params);
    
    return await this.findById(id) as Invoice;
  }

  // Generate unique invoice number
  static async generateInvoiceNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    const query = `
      SELECT COUNT(*) as count 
      FROM invoices 
      WHERE invoice_number LIKE ?
    `;
    
    const prefix = `INV-${year}${month}`;
    const result = await executeQuery(query, [`${prefix}%`]);
    const count = result[0].count + 1;
    
    return `${prefix}-${String(count).padStart(4, '0')}`;
  }

  // Find invoice by ID
  static async findById(id: string): Promise<Invoice | null> {
    const query = `
      SELECT i.*, 
             CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
             u.email as tenant_email, u.phone as tenant_phone,
             l.monthly_rent as lease_rent,
             un.unit_number, p.name as property_name,
             p.address as property_address,
             CONCAT(landlord.first_name, ' ', landlord.last_name) as landlord_name,
             landlord.email as landlord_email
      FROM invoices i
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN tenants t ON i.tenant_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      LEFT JOIN users landlord ON p.landlord_id = landlord.id
      WHERE i.id = ?
    `;
    
    const results = await executeQuery(query, [id]);
    
    if (results.length === 0) {
      return null;
    }
    
    const invoice = results[0];
    
    // Parse JSON fields
    if (invoice.line_items) {
      invoice.line_items = JSON.parse(invoice.line_items);
    }
    
    return invoice;
  }

  // Update invoice
  static async update(id: string, updateData: Partial<Invoice>): Promise<Invoice | null> {
    const allowedFields = [
      'amount', 'tax_amount', 'due_date', 'description', 'line_items',
      'status', 'late_fee_applied', 'discount_amount', 'payment_terms', 'notes'
    ];
    
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key) && updateData[key as keyof Invoice] !== undefined) {
        if (key === 'line_items') {
          updates.push(`${key} = ?`);
          params.push(JSON.stringify(updateData[key as keyof Invoice]));
        } else {
          updates.push(`${key} = ?`);
          params.push(updateData[key as keyof Invoice]);
        }
      }
    });
    
    // Recalculate total amount if amount, tax_amount, or discount_amount changed
    if (updateData.amount !== undefined || updateData.tax_amount !== undefined || updateData.discount_amount !== undefined) {
      const currentInvoice = await this.findById(id);
      if (currentInvoice) {
        const amount = updateData.amount !== undefined ? updateData.amount : currentInvoice.amount;
        const taxAmount = updateData.tax_amount !== undefined ? updateData.tax_amount : currentInvoice.tax_amount;
        const discountAmount = updateData.discount_amount !== undefined ? updateData.discount_amount : currentInvoice.discount_amount;
        
        const totalAmount = amount + taxAmount - discountAmount;
        updates.push('total_amount = ?');
        params.push(totalAmount);
      }
    }
    
    if (updates.length === 0) {
      return await this.findById(id);
    }
    
    params.push(id);
    
    const query = `
      UPDATE invoices 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    await executeQuery(query, params);
    return await this.findById(id);
  }

  // Get all invoices with pagination and filtering
  static async findAll(
    options: PaginationQuery & {
      landlordId?: string;
      tenantId?: string;
      propertyId?: string;
      status?: string;
      invoiceType?: string;
      overdue?: boolean;
    } = {}
  ): Promise<{ invoices: Invoice[]; total: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      landlordId,
      tenantId,
      propertyId,
      status,
      invoiceType,
      overdue
    } = options;
    
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ` AND p.landlord_id = ?`;
      params.push(landlordId);
    }
    
    if (tenantId) {
      whereClause += ` AND i.tenant_id = ?`;
      params.push(tenantId);
    }
    
    if (propertyId) {
      whereClause += ` AND un.property_id = ?`;
      params.push(propertyId);
    }
    
    if (status) {
      whereClause += ` AND i.status = ?`;
      params.push(status);
    }
    
    if (invoiceType) {
      whereClause += ` AND i.invoice_type = ?`;
      params.push(invoiceType);
    }
    
    if (overdue) {
      whereClause += ` AND i.due_date < NOW() AND i.status IN ('sent', 'viewed')`;
    }
    
    if (search) {
      whereClause += ` AND (i.invoice_number LIKE ? OR CONCAT(u.first_name, ' ', u.last_name) LIKE ? OR p.name LIKE ?)`;
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }
    
    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM invoices i
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN tenants t ON i.tenant_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      ${whereClause}
    `;
    
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;
    
    // Get invoices
    const query = `
      SELECT i.*, 
             CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
             u.email as tenant_email,
             un.unit_number, p.name as property_name
      FROM invoices i
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN tenants t ON i.tenant_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      ${whereClause}
      ORDER BY i.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const invoices = await executeQuery(query, params);
    
    // Parse JSON fields
    invoices.forEach((invoice: any) => {
      if (invoice.line_items) {
        invoice.line_items = JSON.parse(invoice.line_items);
      }
    });
    
    return { invoices, total };
  }

  // Mark invoice as sent
  static async markAsSent(id: string): Promise<Invoice | null> {
    return await this.update(id, { status: 'sent' });
  }

  // Mark invoice as viewed
  static async markAsViewed(id: string): Promise<Invoice | null> {
    return await this.update(id, { status: 'viewed' });
  }

  // Mark invoice as paid
  static async markAsPaid(id: string, paymentId?: string): Promise<Invoice | null> {
    const updateData: Partial<Invoice> = { status: 'paid' };
    return await this.update(id, updateData);
  }

  // Apply late fee
  static async applyLateFee(id: string, lateFeeAmount: number): Promise<Invoice | null> {
    const invoice = await this.findById(id);
    if (!invoice || invoice.late_fee_applied) {
      return invoice;
    }
    
    const newAmount = invoice.amount + lateFeeAmount;
    const newTotalAmount = newAmount + invoice.tax_amount - invoice.discount_amount;
    
    return await this.update(id, {
      amount: newAmount,
      total_amount: newTotalAmount,
      late_fee_applied: true
    });
  }

  // Get overdue invoices
  static async getOverdueInvoices(landlordId?: string): Promise<Invoice[]> {
    let whereClause = `WHERE i.due_date < NOW() AND i.status IN ('sent', 'viewed')`;
    const params: any[] = [];
    
    if (landlordId) {
      whereClause += ` AND p.landlord_id = ?`;
      params.push(landlordId);
    }
    
    const query = `
      SELECT i.*, 
             CONCAT(u.first_name, ' ', u.last_name) as tenant_name,
             u.email as tenant_email, u.phone as tenant_phone,
             un.unit_number, p.name as property_name
      FROM invoices i
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN tenants t ON i.tenant_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      ${whereClause}
      ORDER BY i.due_date ASC
    `;
    
    const invoices = await executeQuery(query, params);
    
    // Parse JSON fields
    invoices.forEach((invoice: any) => {
      if (invoice.line_items) {
        invoice.line_items = JSON.parse(invoice.line_items);
      }
    });
    
    return invoices;
  }

  // Generate recurring invoices (monthly rent)
  static async generateRecurringInvoices(): Promise<number> {
    // Get all active leases
    const activeLeases = await executeQuery(`
      SELECT l.*, t.id as tenant_id, un.unit_number, p.name as property_name
      FROM leases l
      JOIN tenants t ON l.tenant_id = t.id
      JOIN units un ON l.unit_id = un.id
      JOIN properties p ON un.property_id = p.id
      WHERE l.status = 'active'
        AND l.end_date >= CURDATE()
    `);
    
    let generatedCount = 0;
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    
    for (const lease of activeLeases) {
      // Check if invoice already exists for this month
      const existingInvoice = await executeQuery(`
        SELECT id FROM invoices 
        WHERE lease_id = ? 
          AND invoice_type = 'rent'
          AND MONTH(issue_date) = ? 
          AND YEAR(issue_date) = ?
      `, [lease.id, currentMonth, currentYear]);
      
      if (existingInvoice.length === 0) {
        // Generate new invoice
        const dueDate = new Date();
        dueDate.setDate(5); // Due on 5th of each month
        if (dueDate < new Date()) {
          dueDate.setMonth(dueDate.getMonth() + 1);
        }
        
        await this.create({
          lease_id: lease.id,
          tenant_id: lease.tenant_id,
          invoice_type: 'rent',
          amount: lease.monthly_rent,
          due_date: dueDate,
          description: `Monthly rent for ${lease.property_name} - Unit ${lease.unit_number}`,
          line_items: [
            {
              description: `Monthly rent - ${lease.property_name} Unit ${lease.unit_number}`,
              quantity: 1,
              unit_price: lease.monthly_rent,
              total: lease.monthly_rent
            }
          ],
          status: 'sent'
        });
        
        generatedCount++;
      }
    }
    
    return generatedCount;
  }

  // Get invoice statistics
  static async getStats(landlordId?: string): Promise<any> {
    let whereClause = '';
    const params: any[] = [];
    
    if (landlordId) {
      whereClause = 'WHERE p.landlord_id = ?';
      params.push(landlordId);
    }
    
    const query = `
      SELECT 
        COUNT(*) as total_invoices,
        COUNT(CASE WHEN i.status = 'draft' THEN 1 END) as draft_invoices,
        COUNT(CASE WHEN i.status = 'sent' THEN 1 END) as sent_invoices,
        COUNT(CASE WHEN i.status = 'paid' THEN 1 END) as paid_invoices,
        COUNT(CASE WHEN i.due_date < NOW() AND i.status IN ('sent', 'viewed') THEN 1 END) as overdue_invoices,
        SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid_amount,
        SUM(CASE WHEN i.status IN ('sent', 'viewed') THEN i.total_amount ELSE 0 END) as total_outstanding_amount,
        SUM(CASE WHEN i.due_date < NOW() AND i.status IN ('sent', 'viewed') THEN i.total_amount ELSE 0 END) as total_overdue_amount
      FROM invoices i
      LEFT JOIN leases l ON i.lease_id = l.id
      LEFT JOIN units un ON l.unit_id = un.id
      LEFT JOIN properties p ON un.property_id = p.id
      ${whereClause}
    `;
    
    const results = await executeQuery(query, params);
    return results[0];
  }
}
