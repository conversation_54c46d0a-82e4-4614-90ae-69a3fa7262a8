import { Router, Request, Response } from 'express';
import { ReportModel } from '../models/Report.js';
import { authorizeRoles } from '../middleware/auth.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';

const router = Router();

// Get financial performance report
router.get('/financial',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { dateFrom, dateTo, propertyId } = req.query as any;
    
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    
    const report = await ReportModel.getFinancialReport(
      landlordId,
      dateFrom ? new Date(dateFrom) : undefined,
      dateTo ? new Date(dateTo) : undefined,
      propertyId
    );
    
    res.json({
      success: true,
      data: report
    });
  })
);

// Get property performance report
router.get('/properties',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    
    const report = await ReportModel.getPropertyPerformanceReport(landlordId);
    
    res.json({
      success: true,
      data: report
    });
  })
);

// Get tenant performance report
router.get('/tenants',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    
    const report = await ReportModel.getTenantPerformanceReport(landlordId);
    
    res.json({
      success: true,
      data: report
    });
  })
);

// Get occupancy report
router.get('/occupancy',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { months = 12 } = req.query as any;
    
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    
    const report = await ReportModel.getOccupancyReport(
      landlordId,
      parseInt(months)
    );
    
    res.json({
      success: true,
      data: report
    });
  })
);

// Get revenue trend report
router.get('/revenue-trend',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { months = 12 } = req.query as any;
    
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    
    const report = await ReportModel.getRevenueTrendReport(
      landlordId,
      parseInt(months)
    );
    
    res.json({
      success: true,
      data: report
    });
  })
);

// Get maintenance report
router.get('/maintenance',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    
    const report = await ReportModel.getMaintenanceReport(landlordId);
    
    res.json({
      success: true,
      data: report
    });
  })
);

// Export report data
router.get('/export/:reportType',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const { reportType } = req.params;
    const { dateFrom, dateTo, format = 'json' } = req.query as any;
    
    const validReportTypes = ['financial', 'properties', 'tenants', 'occupancy', 'revenue', 'maintenance'];
    
    if (!validReportTypes.includes(reportType)) {
      throw new AppError('Invalid report type', 400);
    }
    
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    
    const data = await ReportModel.exportData(
      reportType,
      landlordId,
      dateFrom ? new Date(dateFrom) : undefined,
      dateTo ? new Date(dateTo) : undefined,
      format
    );
    
    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${reportType}-report.csv"`);
      res.send(data);
    } else {
      res.json({
        success: true,
        data
      });
    }
  })
);

// Get dashboard summary (combines multiple reports)
router.get('/dashboard',
  authorizeRoles('landlord', 'property_manager'),
  asyncHandler(async (req: Request, res: Response) => {
    const landlordId = req.user!.role === 'landlord' ? req.user!.id : undefined;
    
    // Get multiple reports in parallel
    const [
      financialReport,
      occupancyReport,
      maintenanceReport
    ] = await Promise.all([
      ReportModel.getFinancialReport(landlordId),
      ReportModel.getOccupancyReport(landlordId, 3), // Last 3 months
      ReportModel.getMaintenanceReport(landlordId)
    ]);
    
    const dashboardData = {
      financial: {
        total_revenue: financialReport.total_revenue,
        outstanding_amount: financialReport.outstanding_amount,
        overdue_amount: financialReport.overdue_amount,
        collection_rate: financialReport.collection_rate,
        monthly_revenue: financialReport.actual_monthly_revenue
      },
      occupancy: {
        occupancy_rate: occupancyReport.current_occupancy_rate,
        total_units: occupancyReport.total_units,
        occupied_units: occupancyReport.occupied_units,
        vacant_units: occupancyReport.vacant_units
      },
      maintenance: {
        total_requests: maintenanceReport.total_requests,
        open_requests: maintenanceReport.open_requests,
        urgent_requests: maintenanceReport.urgent_requests,
        average_completion_days: Math.round(maintenanceReport.average_completion_days || 0)
      },
      properties: {
        total_properties: financialReport.total_properties,
        total_tenants: financialReport.total_tenants,
        active_leases: financialReport.active_leases
      }
    };
    
    res.json({
      success: true,
      data: dashboardData
    });
  })
);

export default router;
