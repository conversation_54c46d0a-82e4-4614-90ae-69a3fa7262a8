import React, { useState, useEffect } from 'react';
import { 
  Building, 
  Users, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle,
  Calendar,
  FileText,
  CreditCard,
  Plus,
  Eye
} from 'lucide-react';
import { dashboardAPI, propertiesAPI } from '../../services/api';

interface DashboardStats {
  totalProperties: number;
  totalUnits: number;
  occupiedUnits: number;
  vacantUnits: number;
  occupancyRate: number;
  monthlyRevenue: number;
  actualRevenue: number;
  pendingInvoices: number;
  overdueInvoices: number;
  totalInvoiceAmount: number;
  maintenanceRequests: number;
  urgentMaintenance: number;
  totalTenants: number;
  newTenantsThisMonth: number;
  leasesExpiringThisMonth: number;
}

const LandlordDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentProperties, setRecentProperties] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch dashboard stats
      const dashboardStats = await dashboardAPI.getStats();
      setStats(dashboardStats);
      
      // Fetch recent properties
      const propertiesResponse = await propertiesAPI.getAll({ limit: 5 });
      setRecentProperties(propertiesResponse.data || []);
      
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const StatCard = ({ title, value, icon: Icon, color, change, changeType }: any) => (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <p className={`text-sm mt-1 ${changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
              {changeType === 'positive' ? '+' : ''}{change}
            </p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Landlord Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back! Here's your property portfolio overview.</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Plus size={16} className="mr-2" />
            Add Property
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <FileText size={16} className="mr-2" />
            Generate Report
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Properties"
          value={stats?.totalProperties || 0}
          icon={Building}
          color="bg-blue-500"
          change={"+2 this month"}
          changeType="positive"
        />
        <StatCard
          title="Occupancy Rate"
          value={`${stats?.occupancyRate || 0}%`}
          icon={Users}
          color="bg-green-500"
          change={"+5% from last month"}
          changeType="positive"
        />
        <StatCard
          title="Monthly Revenue"
          value={`KES ${(stats?.monthlyRevenue || 0).toLocaleString()}`}
          icon={DollarSign}
          color="bg-purple-500"
          change={"+12% from last month"}
          changeType="positive"
        />
        <StatCard
          title="Pending Issues"
          value={stats?.urgentMaintenance || 0}
          icon={AlertTriangle}
          color="bg-red-500"
          change={"-3 from last week"}
          changeType="positive"
        />
      </div>

      {/* Revenue and Occupancy Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Overview */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Revenue Overview</h3>
            <TrendingUp size={20} className="text-green-500" />
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Actual Revenue</span>
              <span className="font-semibold">KES {(stats?.actualRevenue || 0).toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Potential Revenue</span>
              <span className="font-semibold">KES {(stats?.monthlyRevenue || 0).toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Collection Rate</span>
              <span className="font-semibold text-green-600">
                {stats?.actualRevenue && stats?.monthlyRevenue 
                  ? Math.round((stats.actualRevenue / stats.monthlyRevenue) * 100) 
                  : 0}%
              </span>
            </div>
            <div className="pt-2 border-t">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Outstanding Amount</span>
                <span className="font-semibold text-red-600">
                  KES {((stats?.monthlyRevenue || 0) - (stats?.actualRevenue || 0)).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Property Performance */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Property Performance</h3>
            <Building size={20} className="text-blue-500" />
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Units</span>
              <span className="font-semibold">{stats?.totalUnits || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Occupied Units</span>
              <span className="font-semibold text-green-600">{stats?.occupiedUnits || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Vacant Units</span>
              <span className="font-semibold text-red-600">{stats?.vacantUnits || 0}</span>
            </div>
            <div className="pt-2 border-t">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full" 
                  style={{ width: `${stats?.occupancyRate || 0}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {stats?.occupancyRate || 0}% Occupancy Rate
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Properties and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Properties */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Recent Properties</h3>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              View All
            </button>
          </div>
          <div className="space-y-3">
            {recentProperties.slice(0, 4).map((property) => (
              <div key={property.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">{property.name}</p>
                  <p className="text-sm text-gray-600">{property.city}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    {property.occupied_units || 0}/{property.total_units || 0} Units
                  </p>
                  <p className="text-xs text-gray-500">
                    {property.total_units > 0 
                      ? Math.round(((property.occupied_units || 0) / property.total_units) * 100)
                      : 0}% Occupied
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <Plus size={20} className="text-blue-600 mb-2" />
              <p className="font-medium text-gray-900">Add Property</p>
              <p className="text-sm text-gray-600">Create new property</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <Users size={20} className="text-green-600 mb-2" />
              <p className="font-medium text-gray-900">Add Tenant</p>
              <p className="text-sm text-gray-600">Register new tenant</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <FileText size={20} className="text-purple-600 mb-2" />
              <p className="font-medium text-gray-900">Create Invoice</p>
              <p className="text-sm text-gray-600">Generate billing</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <Eye size={20} className="text-orange-600 mb-2" />
              <p className="font-medium text-gray-900">View Reports</p>
              <p className="text-sm text-gray-600">Analytics & insights</p>
            </button>
          </div>
        </div>
      </div>

      {/* Alerts and Notifications */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Alerts & Notifications</h3>
        <div className="space-y-3">
          {stats?.leasesExpiringThisMonth > 0 && (
            <div className="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <Calendar size={20} className="text-yellow-600 mr-3" />
              <div>
                <p className="font-medium text-yellow-800">
                  {stats.leasesExpiringThisMonth} lease(s) expiring this month
                </p>
                <p className="text-sm text-yellow-600">Review and prepare renewal agreements</p>
              </div>
            </div>
          )}
          {stats?.overdueInvoices > 0 && (
            <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
              <CreditCard size={20} className="text-red-600 mr-3" />
              <div>
                <p className="font-medium text-red-800">
                  {stats.overdueInvoices} overdue invoice(s)
                </p>
                <p className="text-sm text-red-600">Follow up on outstanding payments</p>
              </div>
            </div>
          )}
          {stats?.urgentMaintenance > 0 && (
            <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertTriangle size={20} className="text-red-600 mr-3" />
              <div>
                <p className="font-medium text-red-800">
                  {stats.urgentMaintenance} urgent maintenance request(s)
                </p>
                <p className="text-sm text-red-600">Immediate attention required</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LandlordDashboard;
